<template>
  <div class="test-pdf-container">
    <div class="header">
      <h2>PDF预览测试页面</h2>
      <div class="controls">
        <a-input
          v-model:value="testUrl"
          placeholder="输入PDF URL进行测试"
          style="width: 500px; margin-right: 16px;"
        />
        <a-button type="primary" @click="loadPdf">加载PDF</a-button>
        <a-button @click="resetToDefault" style="margin-left: 8px;">重置为默认URL</a-button>
        <a-button @click="testDirectAccess" style="margin-left: 8px;">测试直接访问</a-button>
      </div>
      <div class="options" style="margin-top: 16px;">
        <a-checkbox v-model:checked="forceProxy">强制使用代理</a-checkbox>
        <a-checkbox v-model:checked="disableProxy" style="margin-left: 16px;">禁用代理</a-checkbox>
      </div>
    </div>
    
    <div class="pdf-viewer">
      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="pdfjs" tab="PDF.js预览">
          <PdfReader
            v-if="currentUrl"
            :url="currentUrl"
            :page="currentPage"
            :zoom="zoomLevel"
            :force-proxy="forceProxy"
            :disable-proxy="disableProxy"
            @load="onPdfLoad"
          />
          <div v-else class="placeholder">
            <a-empty description="请输入PDF URL并点击加载" />
          </div>
        </a-tab-pane>
        <a-tab-pane key="iframe" tab="直接iframe预览">
          <iframe
            v-if="currentUrl"
            :src="getDirectUrl()"
            width="100%"
            height="500px"
            style="border: 1px solid #d9d9d9;"
          />
          <div v-else class="placeholder">
            <a-empty description="请输入PDF URL并点击加载" />
          </div>
        </a-tab-pane>
        <a-tab-pane key="link" tab="新窗口打开">
          <div style="text-align: center; padding: 50px;">
            <a-button
              v-if="currentUrl"
              type="primary"
              @click="openInNewWindow"
              size="large"
            >
              在新窗口中打开PDF
            </a-button>
            <p v-else>请先加载PDF URL</p>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
    
    <div class="debug-info">
      <h3>调试信息</h3>
      <div class="info-item">
        <strong>当前URL:</strong> {{ currentUrl }}
      </div>
      <div class="info-item">
        <strong>强制代理:</strong> {{ forceProxy }}
      </div>
      <div class="info-item">
        <strong>禁用代理:</strong> {{ disableProxy }}
      </div>
      <div class="info-item">
        <strong>当前域名:</strong> {{ currentOrigin }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PdfReader from '@/components/PdfReader/index.vue'
import { message } from 'ant-design-vue'

defineOptions({
  name: 'TestPdf'
})

// 默认测试URL
const defaultUrl = 'https://sppgpttest.gcycloud.cn/sftp/file/test/default/20250715/1945052343708422144/[SZCG2025000306-A]深圳大学电化学测试仪采购(1).pdf'

// 响应式数据
const testUrl = ref(defaultUrl)
const currentUrl = ref('')
const currentPage = ref(1)
const zoomLevel = ref(100)
const forceProxy = ref(false)
const disableProxy = ref(false)
const currentOrigin = ref(window.location.origin)

// 方法
const loadPdf = () => {
  if (!testUrl.value.trim()) {
    message.error('请输入PDF URL')
    return
  }
  
  currentUrl.value = testUrl.value.trim()
  message.success('开始加载PDF...')
}

const resetToDefault = () => {
  testUrl.value = defaultUrl
  currentUrl.value = defaultUrl
}

const onPdfLoad = () => {
  message.success('PDF加载完成')
}

const testDirectAccess = async () => {
  try {
    const response = await fetch(testUrl.value)
    if (response.ok) {
      message.success('文件可以直接访问')
      console.log('Direct access successful:', response)
    } else {
      message.error(`直接访问失败: ${response.status} ${response.statusText}`)
    }
  } catch (error: any) {
    message.error(`直接访问失败: ${error?.message || error}`)
    console.error('Direct access error:', error)
  }
}

// 初始化
resetToDefault()
</script>

<style lang="scss" scoped>
.test-pdf-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.header {
  margin-bottom: 16px;
  
  h2 {
    margin-bottom: 16px;
    color: #333;
  }
  
  .controls {
    display: flex;
    align-items: center;
  }
  
  .options {
    display: flex;
    align-items: center;
  }
}

.pdf-viewer {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
  
  .placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.debug-info {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  
  h3 {
    margin-bottom: 12px;
    color: #333;
  }
  
  .info-item {
    margin-bottom: 8px;
    font-size: 14px;
    
    strong {
      color: #666;
    }
  }
}
</style>
