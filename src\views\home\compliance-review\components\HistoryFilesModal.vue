<template>
  <BaseDrawer
    v-model="visible"
    :width="320"
    :loading="loading"
    title="历史文件"
    getContainer=".compliance-review-container"
    rootClassName="customer-history-files-drawer"
    @cancel="handleCancel"
  >

    <div class="history-files-content">
      <div class="files-list">
        <div
          v-for="file in files"
          :key="file.id"
          class="file-item"
          :class="{'active': file.id === selectedFile.id}"
          @click="handlePreview(file)"
        >
          <div class="file-header">
            <div class="file-info">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-meta">
                <span class="file-date">{{ file.date }}</span>
                <span class="file-size">{{ file.size }}</span>
              </div>
            </div> 
          </div>
          <div class="status-badge">
            <component v-if="getExamineResult(file).icon" :is="getExamineResult(file).icon" class="status-icon"
            :style="{color: getExamineResult(file).color}"/>
            <span class="status-text">{{ getExamineResult(file).text }}</span>
          </div>
        </div>

        <div v-if="files.length === 0" class="empty-state">
          <a-empty description="暂无历史文件" />
        </div>
      </div>
    </div>
  </BaseDrawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { getRiskStyle } from '@/views/home/<USER>/examine';
import BaseDrawer from '@/components/BaseDrawer/base-drawer.vue' 


interface HistoryFile {
  id: string
  name: string
  date: string
  size: string
  reviewResult?: number,
  status: 'current' | 'approved' | 'rejected' | 'pending'
  url?: string
}

const props = defineProps<{
  modelValue: boolean
  taskId?: string
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  preview: [file: HistoryFile] 
}>()


const visible = ref(false)
const loading = ref(false)

// 模拟历史文件数据
const files = ref<HistoryFile[]>([
  {
    id: '1',
    name: '[BAC202250010104-A]深圳市宝安区人民医院医疗设备采购项目',
    date: '2024-01-15 10:30:00',
    size: '2.5MB',
    reviewResult: 1,
    status: 'current'
  },
  {
    id: '2', 
    name: '[BAC202250010104-A]深圳市宝安区人民医院医疗设备采购项目',
    date: '2024-01-12 14:20:00',
    size: '2.3MB',
    reviewResult: 0,
    status: 'approved'
  },
  {
    id: '3',
    name: '[BAC202250010104-A]深圳市宝安区人民医院医疗设备采购项目',
    date: '2024-01-10 09:15:00',
    size: '2.1MB',
    reviewResult: 1,
    status: 'rejected'
  }
])
// 选中的
const selectedFile = ref<HistoryFile>(files.value[0])
const handlePreview = (file: HistoryFile) => {
  if(file.id === selectedFile.value.id) return
  selectedFile.value = file
  emit('preview', file)
}

// 监听外部传入的visible状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    loadHistoryFiles()
  }
})

// 监听内部visible状态变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})
// 审查结果颜色
const getExamineResult: any = (item: any) => { 
  return item.id === selectedFile.value.id ? { 
      color: '#133CE8',
      text: '当前文件',
  } : getRiskStyle(item.reviewResult) 
}

// 加载历史文件
const loadHistoryFiles = async () => {
  if (!props.taskId) return
  
  try {
    loading.value = true
    // 这里应该调用API获取历史文件
    // const { data } = await apiGetHistoryFiles(props.taskId)
    // files.value = data
    
    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 500))
  } catch (error) {
    console.error('加载历史文件失败:', error)
    message.error('加载历史文件失败')
  } finally {
    loading.value = false
  }
}


// 取消操作
const handleCancel = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.history-files-content {
  padding: 24px;
  .files-list {
    max-height: 500px;
    overflow-y: auto;
    
    .file-item {
      cursor: pointer;
      padding: 16px;
      border: 1px solid #D9D9D9;
      border-radius: 8px;
      margin-bottom: 12px;
      transition: all 0.2s;
      &.active,
      &:hover {
        border-color: var(--main-3); 
        background-color: var(--main-1);
      }
      
      &:last-child {
        margin-bottom: 0;
      }
      &.active {
        .status-badge {
          padding: 0 8px;
          height: 22px;
          line-height: 22px;
          border-radius: 22px;
          background-color: var(--main-1);
          color: var(--main-6);
          font-size: 12px;
          text-align: center;
        }
      }
      .file-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
        
        .file-info {
          flex: 1;
          
          .file-name {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 4px;
            line-height: 1.4;
          }
          
          .file-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #8c8c8c;
            
            .file-date,
            .file-size {
              display: flex;
              align-items: center;
            }
          }
        }  
      }
      .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        width: fit-content; 
        .status-icon {
          width: 16px;
          height: 16px;
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 40px 0;
    }
  }
  
}
</style>
<style lang="scss">
.customer-history-files-drawer {
  .ant-drawer-header {
    .ant-drawer-header-title {
      position: relative;
    }
    .ant-drawer-close {
      position: absolute;
      right: 0; 
      top: 50%;
      transform: translateY(-50%);
      z-index: 1;
      margin-right: 0;
    } 
  }
}
</style>
