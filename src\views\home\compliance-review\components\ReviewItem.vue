<template>
  <div :class="['review-item', item.riskLevel, { 'editable': editable }]">
    <!-- 头部区域：风险标签 + 描述 + 点赞点踩 -->
    <div class="header-section">
      <div class="content-left">
        <a-tag :class="getRiskStyle(item.reviewItemResult).className" class="tip-tag">
          {{ getRiskStyle(item.reviewItemResult).text }}
        </a-tag>
        <div class="item-description">{{ item.fileText }}</div>
      </div>
      <div class="vote-section">
        <div class="vote-buttons">
          <div
            class="vote-btn"
            :class="{ active: currentVote === 'like' }"
            @click="handleLike"
          > 
            <ThumbsUp class="icon" :size="16" :color="currentVote === 'like' ? 'var(--main-6)':'rgba(0, 0, 0, 0.65)'"/>
            <span>{{ item.likeCount || 0 }}</span>
          </div>
          <div
            class="vote-btn"
            :class="{ active: currentVote === 'dislike' }"
            @click="handleDislike"
          >
            <ThumbsDown class="icon" :size="16" :color="currentVote === 'dislike' ? 'var(--main-6)':'rgba(0, 0, 0, 0.65)'"/> 
            <span>{{ item.dislikeCount || 0 }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="item.riskWarning" class="mod-box risk-details">
      <span class="label">风险提示：</span>
      <span class="content">{{ item.riskWarning }}</span>
    </div>
    <div v-if="item.legalBasicSourceList" class="mod-box legal-basis">
      <span class="label">审查依据：</span> 
      <div class="source" v-for="(article,index) in item.legalBasicSourceList" :key="index">
        <span class="link" @click="handleOpenLink(article.sourceLink)">{{ article.source }}{{article.basicNumber}}{{article.basicIssue}}：</span>
        <span class="content">{{ article.basicDesc }}</span>
      </div>
    </div>
    <div v-if="item.editSuggestion" class="mod-box suggestion">
      <span v-if="!isEditingMode" class="label">建议修改为：</span>
      <div v-if="isEditingMode" class="suggestion-content"> 
        <a-input placeholder="请输入建议修改内容"
          v-model:value="editingSuggestion" 
          :maxlength="500"  
          ref="editTextarea"
        />
          <!-- @blur="handleSaveEdit"@keydown.enter.ctrl="handleSaveEdit"
          @keydown.esc="handleCancelEdit" -->
        <div class="edit-actions">
          <a-button type="primary" @click="handleSaveEdit">确认</a-button>
          <a-button  @click="handleCancelEdit">取消</a-button>
        </div>
      </div>
      <template v-else>
        <span class="content" @click="handleStartEdit">"{{ item.editSuggestion }}"</span>
        <SquarePen class="btn edit-icon" :size="16" color="#6B7280" @click="handleStartEdit"/>
      </template>
    </div>
    <div class="action-buttons">
      <!-- 待处理状态：显示接受建议和不接受建议按钮 -->
      <template v-if="itemStatus === 'pending'">
        <a-button
          v-if="item.reviewResult === 1"
          type="primary"
          size="small"
          class="btn-agree"
          @click="handleAcceptSuggestion"
        >
          <template #icon>
            <PenLine :size="16" color="#fff" />
          </template>
          接受建议并修订
        </a-button>
        <a-button
          size="small"
          class="btn-disagree"
          @click="handleRejectSuggestion"
        >
          <template #icon>
            <X :size="16" color="rgba(0, 0, 0, 0.65)"/>
          </template>
          不接受建议
        </a-button>
      </template>

      <!-- 已接受状态：显示已修改和撤回按钮 -->
      <template v-if="itemStatus === 'accepted'">
        <a-button
          size="small"
          class="btn-modify"
          @click="handleMarkAsModified"
        >
          <template #icon>
            <CircleCheck :size="16" color="#52C41A"/>
          </template>
          已修改
        </a-button>
        <a-popover
          v-model:open="withdrawModalVisible"
          placement="bottom"
          trigger="manual"
          :arrow="true"
          overlay-class-name="withdraw-confirm-popover"
        >
          <template #content>
            <div class="withdraw-confirm-content">
              <div class="tip-section">
                <div class="tip-icon">
                  <div class="info-icon">i</div>
                </div>
                <div class="tip-text">
                  撤回后数据会返回原始版本，<br />确定执行？
                </div>
              </div>
              <div class="button-group">
                <a-button @click="handleWithdrawCancel">取消</a-button>
                <a-button type="primary" @click="handleWithdrawConfirm">确定</a-button>
              </div>
            </div>
          </template>
          <a-button
            ref="withdrawButtonRef1"
            size="small"
            class="btn-disagree"
            @click="handleShowWithdrawModal"
          >
            <template #icon>
              <CornerUpLeft :size="16" color="rgba(0, 0, 0, 0.65)"/>
            </template>
            撤回
          </a-button>
        </a-popover>
      </template> 
    </div>

    <!-- 点踩弹框 -->
    <DislikeModal
      v-model="dislikeModalVisible"
      @confirm="handleDislikeConfirm"
      @cancel="handleDislikeCancel"
    /> 
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { getRiskStyle } from '@/views/home/<USER>/examine'
import { SquarePen,X,PenLine,CircleCheck,CornerUpLeft, ThumbsUp, ThumbsDown } from 'lucide-vue-next'
import DislikeModal from './DislikeModal.vue'

defineOptions({
  name: 'ReviewItem'
})

interface Props {
  item: Record<string,any>
  editable?: boolean
  initialVote?: 'like' | 'dislike' | ''
}

const props = withDefaults(defineProps<Props>(), {
  editable: false,
  initialVote: ''
})

// 项目状态：pending(待处理), accepted(已接受), modified(已修改), withdrawn(已撤回)
const itemStatus = ref<'pending' | 'accepted' | 'modified' | 'withdrawn'>('pending')

const emit = defineEmits<{
  'update': [item: Record<string,any>]
  'verify': [item: Record<string,any>]
  'cancel': [item: Record<string,any>]
  'edit': [item: Record<string,any>]
  'like': [item: Record<string,any>, reason?: string]
  'dislike': [item: Record<string,any>, reason?: string]
}>()

// 当前投票状态
const currentVote = ref<'like' | 'dislike' | ''>(props.initialVote)

// 弹框状态管理
const dislikeModalVisible = ref(false)
const withdrawModalVisible = ref(false)

// 撤回按钮引用
const withdrawButtonRef1 = ref<any>(null)

// 内联编辑状态管理
const isEditingMode = ref(false)
const editingSuggestion = ref('')
const editTextarea = ref()

// 点赞处理
const handleLike = () => {
  if (currentVote.value === 'like') {
    // 如果已经点赞，则取消点赞
    currentVote.value = ''
    emit('like', props.item, '')
  } else {
    // 点赞
    currentVote.value = 'like'
    emit('like', props.item)
  }
}

// 点踩处理
const handleDislike = () => {
  if (currentVote.value === 'dislike') {
    // 如果已经点踩，则取消点踩
    currentVote.value = ''
    emit('dislike', props.item, '')
  } else {
    // 显示点踩弹框
    dislikeModalVisible.value = true
  }
}

// 处理点踩弹框确认
const handleDislikeConfirm = (reasons: string[], otherOpinion: string) => {
  currentVote.value = 'dislike'

  // 组合反馈原因
  const feedbackReasons = [...reasons]
  if (otherOpinion.trim()) {
    feedbackReasons.push(otherOpinion.trim())
  }

  const reasonText = feedbackReasons.join('; ')
  emit('dislike', props.item, reasonText)
}
// 处理点踩弹框取消
const handleDislikeCancel = () => {
  // 取消操作，不做任何处理
}

// 接受建议并修订
const handleAcceptSuggestion = () => {
  itemStatus.value = 'accepted'
  emit('verify', props.item)
}

// 不接受建议
const handleRejectSuggestion = () => {
  emit('cancel', props.item)
}

// 标记为已修改
const handleMarkAsModified = () => {
  itemStatus.value = 'modified'
  emit('update', { ...props.item, status: 'modified' })
}

// 显示撤回弹框
const handleShowWithdrawModal = () => {
  withdrawModalVisible.value = true
}

// 撤回确认
const handleWithdrawConfirm = () => {
  itemStatus.value = 'pending'
  emit('update', { ...props.item, status: 'pending' })
}

// 撤回取消
const handleWithdrawCancel = () => {
  withdrawModalVisible.value = false
}

// 内联编辑建议
const handleStartEdit = () => {
  isEditingMode.value = true
  editingSuggestion.value = props.item.editSuggestion || ''

  // 下一帧聚焦到输入框
  nextTick(() => {
    if (editTextarea.value) {
      editTextarea.value.focus()
    }
  })
}

// 保存编辑
const handleSaveEdit = () => {
  if (editingSuggestion.value.trim() !== props.item.editSuggestion) {
    emit('update', { ...props.item, editSuggestion: editingSuggestion.value.trim() })
  }
  isEditingMode.value = false
}

// 取消编辑
const handleCancelEdit = () => {
  editingSuggestion.value = props.item.editSuggestion || ''
  isEditingMode.value = false
}
// 打开链接
const handleOpenLink = (url: string) => {
  window.open(url, '_blank')
}
</script>

<style lang="scss" scoped>
.review-item {
  padding: 16px;
  border-bottom: 1px solid #E5E7EB;
  background: var(--fill-0);
  transition: all 0.2s; 
  display: flex;
  flex-direction: column;
  gap: 8px;
  &.active,
  &:hover {
    background-color: #DCE8FF;
  }
  .tip-tag {
    display: block; 
    height: 26px;
    line-height: 26px;
    padding: 0 8px;
    font-weight: 400;
    background-color: #fafafa;
    border-color: #d9d9d9;
    color: #8c8c8c;
    &.risk {
      background-color: #fff2f0;
      border-color: #ffccc7;
      color: #ff4d4f;
    }

    &.safe{
      background-color: #F6FFED;
      border-color: #B7EB8F;
      color: #52C41A;
    } 
  }
  .header-section {
    display: flex; 
    justify-content: space-between; 

    .content-left {
      flex: 1;
      display: flex; 
      .item-description {
        font-size: 16px; 
      }
    }

    .vote-section {
      flex-shrink: 0;

      .vote-buttons {
        display: flex;
        align-items: center;
        gap: 16px;

        .vote-btn {
          display: flex;
          align-items: center;
          gap: 4px;  
          cursor: pointer;  
        }
      }
    }
  }
  .mod-box {
    display: flex; 
    width: 100%;
    .label {
      flex-shrink: 0;
    } 
    .content { 
      white-space: break-spaces;
      color: #374151;
    }
    .btn {
      margin-left: 8px;
      cursor: pointer;
    } 
  }  
  .suggestion {
    align-items:center;
  }
 .legal-basis {
  .link {
    cursor: pointer;
    color: var(--main-6);
    &:hover {
      color: var(--main-5);
    }
    &:focus,
    &:active {
      color: var(--main-7);
    }
  }
 }
  .suggestion-content {
    flex: 1; 
    display: flex;
    align-items: center;
    justify-content: space-between;
    .edit-actions {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 4px;
      margin-left: 8px;
    } 
    :deep(.ant-input) {
      border-radius: 4px;
      &:focus {
        border-color: #D9D9D9;
        box-shadow: none;
      }
    }
    .ant-btn {
      padding: 4px 9px;
      font-size: 12px;
    }
  } 
  .btn-disagree {
    border-color: #D1D5DB;
    color: rgba(0, 0, 0, 0.65);
    &:hover{
      border-color: #D1D5DB;
      color: rgba(0, 0, 0, 0.65);
    }
  }
  .action-buttons {
    display: flex;
    gap: 8px;
    .ant-btn {
      height: 28px;
      font-size: 12px;
      padding: 0 12px;
      border-radius: 6px; 
      display: flex;
      align-items: center;
      gap: 4px;
      transition: all 0.3s ease-in-out;
      &:hover{
        opacity: 0.8;
      }
    }
    .btn-modify {
      background-color: #F6FFED;
      border-color: #B7EB8F;
      color: #52C41A;  
    }
  }
}

// 撤回确认Popover样式
:deep(.withdraw-confirm-popover) {
  .ant-popover-content {
    padding: 0;
  }

  .ant-popover-inner {
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
                0 3px 6px -4px rgba(0, 0, 0, 0.12),
                0 9px 28px 8px rgba(0, 0, 0, 0.05);
    min-width: 320px;
    max-width: 400px;
  }

  // 确保箭头显示
  .ant-popover-arrow {
    display: block !important;
  }
}

.withdraw-confirm-content {
  .tip-section {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;

    .tip-icon {
      flex-shrink: 0;
      margin-top: 2px;

      .info-icon {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background-color: var(--main-6, #1890ff);
        color: #fff;
        font-size: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
      }
    }

    .tip-text {
      flex: 1;
      font-size: 14px;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.88);
    }
  }

  .button-group {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .ant-btn {
      height: 32px;
      padding: 0 15px;
      border-radius: 6px;
      font-size: 14px;

      &:first-child {
        color: rgba(0, 0, 0, 0.88);
        border-color: #d9d9d9;
        background: #fff;

        &:hover {
          color: var(--main-6, #1890ff);
          border-color: var(--main-6, #1890ff);
        }
      }

      &[type="primary"] {
        background-color: var(--main-6, #1890ff);
        border-color: var(--main-6, #1890ff);

        &:hover {
          background-color: var(--main-5, #40a9ff);
          border-color: var(--main-5, #40a9ff);
        }
      }
    }
  }
}
</style>
