import type { ReviewTask, ReviewItem, CheckList, CheckListItem } from '@/types/compliance'

// 模拟审查任务数据
export const mockReviewTask: ReviewTask = {
  id: '1',
  taskName: '深圳市宝安区人民医院皮肤科设备采购审查',
  fileName: '深圳市宝安区人民医院皮肤科设备一批采购.pdf',
  fileUrl: '/static/sample.pdf',
  fileId: 'mock-file-id-123',
  reviewTime: '2024-01-15 09:30:00',
  status: 'in_progress',
  totalItems: 16,
  riskItems: 6,
  safeItems: 5,
  naItems: 5,
  items: []
}

// 模拟审查项目数据
export const mockReviewItems: any[] = [
  {
    id: '1',
    reviewItemName: '资格公平性检查',
    reviewItemCode: 'qual_fairness',
    reviewItemResult: 1,
    errorNum: 3,
    pointNum: 3,
    children: [
      {
        id: '1-1',
        pointId: '1',
        pointResult: 1,
        resultId: '3',
        reviewResult: 1,
        reviewItemResult: 1,
        fileText: '投标人应从其银行账户（基本存款账户）资质下列方式：公司公转账方式向招标文件载明的投标保证金账户交投标保证金，具体金额详见招标文件第一章。',
        riskWarning: '投标人应从其银行账户（基本存款账户）资质下列方式：公司公转账方式向招标文件载明的投标保证金账户交投标保证金，具体金额详见招标文件第一章。的要求限定投标人缴纳保证金的形式， 违反了《招标投标领域公平竞争审查规则》中的规定，建议删除。',
        legalBasicSourceList: [{
          "source": "《中华人民共和国政府采购法》",
          basicIssue: 'aaas',
          basicNumber:'法律条款',
          basicDesc:'政策制定机关可以通过招标投标信用评价引导经营主体诚信经营活动，可以根据实际情况制定实施相应政策措施或制定经营主体应用信用评价结果，但应平等对待不同地区、所有制形式的经营主体，依法保障经营主体自主权，不得制定以下政策措施：\n（三）限定经营主体缴纳保证金的形式'
        }],
        legalBasic: '政策制定机关可以通过招标投标信用评价引导经营主体诚信经营活动，可以根据实际情况制定实施相应政策措施或制定经营主体应用信用评价结果，但应平等对待不同地区、所有制形式的经营主体，依法保障经营主体自主权，不得制定以下政策措施：（三）限定经营主体缴纳保证金的形式',
        editSuggestion: '删除对投标保证金缴纳形式的限制性要求',
        page: 3,
        position: [
          {
            page: 3,
            x1: 327,
            y1: 616,
            x2: 757,
            y2: 736
          }
        ],
        riskLevel: 'risk',
        details: '投标人应从其银行账户（基本存款账户）资质下列方式：公司公转账方式向招标文件载明的投标保证金账户交投标保证金，具体金额详见招标文件第一章。的要求限定投标人缴纳保证金的形式， 违反了《招标投标领域公平竞争审查规则》中的规定，建议删除。',
        legalBasis: '《招标投标领域公平竞争审查规则》第二章第十一条',
        suggestion: '删除对投标保证金缴纳形式的限制性要求',
        likeCount: 2,
        dislikeCount: 0
      },
      {
        id: '1-2',
        pointId: '2',
        pointResult: 1,
        resultId: '4',
        reviewResult: 1,
        reviewItemResult: 1,
        fileText: '投标人须具有独立法人资格，注册资本不少于500万元人民币。',
        riskWarning: '对投标人注册资本的要求可能构成不合理的准入门槛，违反了公平竞争原则。',
        legalBasicChapter: '《招标投标领域公平竞争审查规则》第二章第八条',
        legalBasic: '不得设定不合理的准入和退出条件，包括但不限于：（一）设定明显超过招标项目实际需要的资格、技术、商务条件等',
        editSuggestion: '删除注册资本限制或调整为合理水平',
        page: 5,
        position: [
          {
            page: 5,
            x1: 100,
            y1: 200,
            x2: 500,
            y2: 250
          }
        ],
        riskLevel: 'risk',
        details: '对投标人注册资本的要求可能构成不合理的准入门槛，违反了公平竞争原则。',
        legalBasis: '《招标投标领域公平竞争审查规则》第二章第八条',
        suggestion: '删除注册资本限制或调整为合理水平',
        likeCount: 1,
        dislikeCount: 1
      },
      {
        id: '1-3',
        pointId: '3',
        pointResult: 1,
        resultId: '5',
        reviewResult: 1,
        reviewItemResult: 1,
        fileText: '投标人必须为本地注册企业，外地企业不得参与投标。',
        riskWarning: '限制外地企业参与投标，构成地域歧视，严重违反公平竞争原则。',
        legalBasicChapter: '《招标投标领域公平竞争审查规则》第二章第九条',
        legalBasic: '不得对不同地区的经营主体实施歧视性政策措施，包括但不限于：（一）对外地经营主体设定歧视性资质要求、评审标准或者不给予同等待遇',
        editSuggestion: '删除地域限制条款，允许所有符合条件的企业参与',
        page: 6,
        position: [
          {
            page: 6,
            x1: 150,
            y1: 300,
            x2: 600,
            y2: 350
          }
        ],
        riskLevel: 'risk',
        details: '限制外地企业参与投标，构成地域歧视，严重违反公平竞争原则。',
        legalBasis: '《招标投标领域公平竞争审查规则》第二章第九条',
        suggestion: '删除地域限制条款，允许所有符合条件的企业参与',
        likeCount: 5,
        dislikeCount: 0
      }
    ]
  },
  {
    id: '2',
    reviewItemName: '技术规格合理性检查',
    reviewItemCode: 'tech_spec',
    reviewItemResult: 0,
    errorNum: 0,
    pointNum: 2,
    children: [
      {
        id: '2-1',
        pointId: '4',
        pointResult: 0,
        resultId: '6',
        reviewResult: 0,
        reviewItemResult: 0,
        fileText: '设备应符合国家相关标准，具备CE认证或同等认证。',
        riskWarning: '',
        legalBasicChapter: '',
        legalBasic: '',
        editSuggestion: '',
        page: 8,
        position: [
          {
            page: 8,
            x1: 200,
            y1: 400,
            x2: 550,
            y2: 450
          }
        ],
        riskLevel: 'safe',
        details: '',
        legalBasis: '',
        suggestion: '',
        likeCount: 3,
        dislikeCount: 0
      },
      {
        id: '2-2',
        pointId: '5',
        pointResult: 0,
        resultId: '7',
        reviewResult: 0,
        reviewItemResult: 0,
        fileText: '设备技术参数应满足医疗使用需求，具备完善的售后服务体系。',
        riskWarning: '',
        legalBasicChapter: '',
        legalBasic: '',
        editSuggestion: '',
        page: 9,
        position: [
          {
            page: 9,
            x1: 180,
            y1: 500,
            x2: 580,
            y2: 550
          }
        ],
        riskLevel: 'safe',
        details: '',
        legalBasis: '',
        suggestion: '',
        likeCount: 2,
        dislikeCount: 0
      }
    ]
  },
  {
    id: '3',
    reviewItemName: '商务条款审查',
    reviewItemCode: 'business_terms',
    reviewItemResult: 1,
    errorNum: 1,
    pointNum: 2,
    children: [
      {
        id: '3-1',
        pointId: '6',
        pointResult: 1,
        resultId: '8',
        reviewResult: 1,
        reviewItemResult: 1,
        fileText: '付款方式：合同签订后预付30%，设备到货验收合格后支付60%，质保期满后支付剩余10%。',
        riskWarning: '预付款比例过高，可能增加采购风险，建议调整付款比例。',
        legalBasicChapter: '《政府采购法实施条例》第三十七条',
        legalBasic: '采购人应当按照政府采购合同约定的技术、服务、安全标准组织验收，并及时向供应商支付采购资金',
        editSuggestion: '调整预付款比例为不超过20%',
        page: 12,
        position: [
          {
            page: 12,
            x1: 120,
            y1: 300,
            x2: 520,
            y2: 380
          }
        ],
        riskLevel: 'risk',
        details: '预付款比例过高，可能增加采购风险，建议调整付款比例。',
        legalBasis: '《政府采购法实施条例》第三十七条',
        suggestion: '调整预付款比例为不超过20%',
        likeCount: 1,
        dislikeCount: 2
      },
      {
        id: '3-2',
        pointId: '7',
        pointResult: 0,
        resultId: '9',
        reviewResult: 0,
        reviewItemResult: 0,
        fileText: '质保期为设备验收合格后2年，质保期内免费维修保养。',
        riskWarning: '',
        legalBasicChapter: '',
        legalBasic: '',
        editSuggestion: '',
        page: 13,
        position: [
          {
            page: 13,
            x1: 160,
            y1: 200,
            x2: 480,
            y2: 250
          }
        ],
        riskLevel: 'safe',
        details: '',
        legalBasis: '',
        suggestion: '',
        likeCount: 4,
        dislikeCount: 0
      }
    ]
  },
  {
    id: '4',
    reviewItemName: '评标方法检查',
    reviewItemCode: 'eval_method',
    reviewItemResult: 2,
    errorNum: 0,
    pointNum: 1,
    children: [
      {
        id: '4-1',
        pointId: '8',
        pointResult: 2,
        resultId: '10',
        reviewResult: 2,
        reviewItemResult: 2,
        fileText: '本项目采用综合评分法，技术分40分，商务分35分，价格分25分。',
        riskWarning: '',
        legalBasicChapter: '',
        legalBasic: '',
        editSuggestion: '',
        page: 15,
        position: [
          {
            page: 15,
            x1: 200,
            y1: 100,
            x2: 500,
            y2: 150
          }
        ],
        riskLevel: 'na',
        details: '',
        legalBasis: '',
        suggestion: '',
        likeCount: 0,
        dislikeCount: 0
      }
    ]
  }
]

// 模拟审查清单数据
export const mockCheckList: CheckList = {
  id: 'checklist-1',
  name: '政府采购合规性审查清单',
  version: 'v2.1',
  categories: ['资质审查', '技术评审', '商务评审', '合同审查', '其他事项'],
  items: [
    {
      id: 'item-1',
      category: '资质审查',
      title: '营业执照有效性',
      description: '检查供应商营业执照是否在有效期内',
      required: true,
      completed: false,
      notes: '营业执照已过期，需要更新'
    },
    {
      id: 'item-2',
      category: '资质审查',
      title: '相关资质证书',
      description: '检查是否具备相关行业资质证书',
      required: true,
      completed: true,
    },
    {
      id: 'item-3',
      category: '技术评审',
      title: '技术参数符合性',
      description: '验证设备技术参数是否符合要求',
      required: true,
      completed: true,
    },
    {
      id: 'item-4',
      category: '技术评审',
      title: '技术方案可行性',
      description: '评估技术方案的可行性和先进性',
      required: true,
      completed: false
    },
    {
      id: 'item-5',
      category: '商务评审',
      title: '价格合理性',
      description: '分析投标价格的合理性',
      required: true,
      completed: true,
      riskLevel: 'safe'
    },
    {
      id: 'item-6',
      category: '商务评审',
      title: '付款条件',
      description: '检查付款条件是否合理',
      required: false,
      completed: false
    },
    {
      id: 'item-7',
      category: '合同审查',
      title: '合同条款完整性',
      description: '检查合同条款是否完整',
      required: true,
      completed: false,
      riskLevel: 'risk',
      notes: '部分条款表述不清'
    },
    {
      id: 'item-8',
      category: '合同审查',
      title: '违约责任条款',
      description: '检查违约责任条款是否明确',
      required: true,
      completed: false
    },
    {
      id: 'item-9',
      category: '其他事项',
      title: '环保要求',
      description: '检查是否符合环保要求',
      required: false,
      completed: true,
      riskLevel: 'na',
      notes: '该项目不涉及环保要求'
    },
    {
      id: 'item-10',
      category: '其他事项',
      title: '安全要求',
      description: '检查是否符合安全要求',
      required: false,
      completed: false
    }
  ],
  createdAt: '2024-01-15 08:00:00',
  updatedAt: '2024-01-15 10:45:00'
}

// 模拟PDF标注数据
export const mockPdfAnnotations = [
  {
    id: 'annotation-1',
    itemId: '1',
    pageNumber: 3,
    coordinates: {
      x: 100,
      y: 200,
      width: 300,
      height: 50
    },
    annotationType: 'risk',
    content: '供应商资质存在问题',
    createdAt: '2024-01-15 09:30:00'
  },
  {
    id: 'annotation-2',
    itemId: '2',
    pageNumber: 8,
    coordinates: {
      x: 150,
      y: 300,
      width: 400,
      height: 80
    },
    annotationType: 'safe',
    content: '技术规格符合要求',
    createdAt: '2024-01-15 09:45:00'
  }
]

// 导出所有模拟数据
export const mockData = {
  reviewTask: mockReviewTask,
  reviewItems: mockReviewItems,
  checkList: mockCheckList,
  pdfAnnotations: mockPdfAnnotations
}
