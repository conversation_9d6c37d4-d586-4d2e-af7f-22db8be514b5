<template>
  <div class="library">
    <!-- 审查结果列表 -->
    <div class="result-summary-section">
      <div class="summary-header">审查结果列表</div>
      <div class="summary-stats-bar">
        <div class="stat-group">
          <span class="stat-label">审查总量</span>
          <span class="stat-number total">{{ dashboardData.totalCount }}</span>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-group">
          <span class="stat-label">未发现风险</span>
          <span class="stat-number safe">{{ dashboardData.noRiskCount }}</span>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-group">
          <span class="stat-label">发现风险</span>
          <span class="stat-number risk">{{ dashboardData.riskFoundCount }}</span>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-group">
          <span class="stat-label">处理中</span>
          <span class="stat-number pending">{{ dashboardData.pendingCount }}</span>
        </div>
      </div>
    </div>

    <!-- 筛选和操作区域 -->
    <div class="filter-section">
      <div class="filter-left">
        <a-space :size="12">
          <!-- 文件名搜索 -->
          <div class="search-input">
            <a-input
              v-model:value="form.fileName"
              placeholder="输入名称或编号"
              allow-clear
              :maxLength="100"
              style="width: 200px;"
              @input="onFileNameInput"
            >
              <template #suffix>
                <SearchOutlined class="search-icon" />
              </template>
            </a-input>
          </div>
          <!-- 审查结果筛选 -->
          <a-select
            v-model:value="form.reviewStatus"
            placeholder="审查结果"
            allow-clear
            style="width: 128px;"
            @change="onResultChange"
          >
            <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>

          <!-- 日期范围选择 -->
          <a-range-picker
            v-model:value="dateRange"
            format="YYYY-MM-DD"
            :placeholder="['开始日期', '结束日期']"
            style="width: 360px;"
            @change="onDateRangeChange"
          />
          <!-- 重置按钮 -->
          <a-button class="reset-button" @click="resetFilters">
            <template #icon>
              <RotateCcw :size="16"/>
            </template> 
          </a-button>
        </a-space>
      </div>

      <div class="filter-right">
        <a-space :size="12">
          <!-- 批量操作 -->
          <div class="batch-operation" :class="{ 'has-selection': selectedRowKeys.length > 0 }">
            <div class="batch-info">
              <span class="batch-selected">已选择 {{ selectedRowKeys.length }} 项</span>
            </div>
            <a-button
              class="batch-delete-button"
              :disabled="selectedRowKeys.length === 0 || dialogOpen"
              type="primary" danger ghost
              @click="doBatchDelete($event)">
              批量删除
            </a-button>
          </div>

          <!-- 新建审查任务 -->
          <a-button type="primary" @click="router.push({ name: 'HomeIndex' })">
            <template #icon>
              <PlusOutlined />
            </template>
            新建审查
          </a-button>
        </a-space>
      </div>
    </div>

    <a-table
      :data-source="tableData"
      :columns="columns"
      :pagination="pagination"
      :row-selection="rowSelection"
      :row-key="(record: any) => record.taskId"
      class="table"
      @change="onTableChange">
      <template #bodyCell="{ column, index, record, text }">
         <template v-if="column.dataIndex === 'projectInfo'">
          <div class="project-info"> 
            <template v-if="record.projectCode || record.projectName">
              <div class="project-title">{{ record.projectName }}</div>
              <div class="project-code">{{ record.projectCode }}</div>
            </template>
            <template v-else>-</template>
          </div>
        </template>
        <template v-if="column.dataIndex === 'reviewStatus'">
          <div class="flex items-center">
            <!-- <a-tooltip :title="isOverFiveMinute(record) ? '系统正在为您努力审查，请耐心等待': ''" > -->
              <div class="status-badge">
                <component :is="getExamineResult(record).icon" class="status-icon" :style="{color: getExamineResult(record).color}"/>
                <span class="status-text">{{ getExamineResult(record).text }}</span>
              </div>
            <!-- </a-tooltip> -->
          </div>
        </template>

        <template v-else-if="column.dataIndex === 'reviewResult'">
          <a-tag v-if="typeof text === 'number'" class="result-tag" :color="riskOptions[text].color">{{ riskOptions[text].label}}</a-tag>
          <span v-else>-</span>
        </template>

        <template v-else-if="column.dataIndex === 'handle'">
          <div class="btns">
            <a-button
            v-if="record.reviewStatus === 2 || record.reviewStatus === 1"
              type="text" 
              @click="goResult(record)">
              <template #icon>
                <a-tooltip title="查看">
                  <FileSearch :size="16" />
                </a-tooltip>
                </template>
            </a-button>
            <a-button
              v-if="record.reviewStatus === 2"
              type="text"  @click="exportReport(record)">
              <template #icon>
                <a-tooltip title="导出报告">
                  <ArrowDownToLine  :size="16" />
                </a-tooltip>
                </template>
            </a-button>
            <a-button  type="text" danger @click="doDel(record, $event)">
              <template #icon>
                <a-tooltip title="删除">
                  <Trash  :size="16" />
                </a-tooltip>
                </template>
            </a-button>
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <!-- 删除确认Popover -->
  <a-popover
    v-model:open="dialogOpen"
    :placement="batchDeleteMode ? 'bottom':'bottomLeft'"
    trigger="manual"
    :overlay-class-name="'delete-confirm-popover'"
  >
    <template #content>
      <div class="delete-confirm-content"> 
        <div class="main-box">
          <CircleAlert class="warning-icon" :size="16" color="var(--error-6)"/> 
          <div class="delete-message">
            <div v-if="batchDeleteMode">
              您确定要删除选中的 {{ selectedRowKeys.length }} 个文件吗？<br/>删除后需要重新上传。
            </div>
            <div v-else>
              您确定要删除此文件吗？删除后<br/>需要重新上传。
            </div>
          </div>
        </div>
        <div class="delete-dialog-footer">
          <a-button @click="dialogOpen = false" size="small">取消</a-button>
          <a-button type="primary" danger size="small" @click="doDialogOk">确认删除</a-button>
        </div>
      </div>
    </template>
    <!-- 空的触发元素，用于定位 -->
    <div ref="deletePopoverTrigger" style="position: absolute; visibility: hidden;"></div>
  </a-popover>
</template>

<script setup lang="ts">
import { onMounted, ref, onUnmounted, computed, reactive } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import {SearchOutlined, InfoOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { Trash,RotateCcw , FileSearch ,ArrowDownToLine, CircleAlert} from 'lucide-vue-next'
import {useRouter} from 'vue-router'
import {  apiTaskDelete, apiTaskList, taskDashboard } from '@/api/examine';
import {apiReviewImport} from '@/api/download';
import {message} from 'ant-design-vue'
import {riskOptions,  getStatusStyle,getRiskStyle, statusOptions} from '@/views/home/<USER>/examine';


const router = useRouter()
const record2Delete = ref<any>(null)
const defaultPageSize = ref(10)
const deletePopoverTrigger = ref<HTMLElement>()

// 数据看板数据
const dashboardData = ref({
  totalCount: 0,        // 审查文件总量
  riskFoundCount: 0,    // 发现风险文件数量
  noRiskCount: 0,       // 未发现风险文件数量
  pendingCount: 0,      // 处理中任务数量
  errorCount: 0         // 审查失败任务数量
})
//  表格
const tableData = ref([])
let form = reactive({
  fileName: '',
  reviewStatus: undefined as number | undefined,
  reviewResult: undefined as number | undefined
})
// 审查结果
const getExamineResult = (item: any) => { 
  return (item.reviewResult === 0 || item.reviewResult === 1)? getRiskStyle(item.reviewResult) :getStatusStyle(item.reviewStatus)
}
// 日期范围选择
const dateRange = ref()

// 批量选择
const selectedRowKeys = ref<string[]>([])
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  },
  onSelectAll: (selected: boolean, selectedRows: any[], changeRows: any[]) => {
    console.log('onSelectAll', selected, selectedRows, changeRows)
  },
  onSelect: (record: any, selected: boolean, selectedRows: any[]) => {
    console.log('onSelect', record, selected, selectedRows)
  }
}))
//
const pagination = ref({
  current: 1,
  pageSize: defaultPageSize.value,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `共 ${total} 条记录，显示 ${range[0]}-${range[1]} 条`,
  pageSizeOptions: ['10', '20', '50', '100'],
  showLessItems: true
})

const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    fixed: 'left',
    width: 80,
    customRender: (row: any) => {
      return row.index + 1
    }
  },
  {
    title: '采购项目名称/编号',
    dataIndex: 'projectInfo',
    ellipsis: true
  },
  {title: '文件名称', dataIndex: 'fileName', ellipsis: true},
  {title: '审查结果', dataIndex: 'reviewStatus', width: 130},
  {title: '创建人', dataIndex: 'createUserName', width: 100},
  {title: '创建时间', dataIndex: 'createTime', width: 180},
  // {title: '更新时间', dataIndex: 'updaterTime', width: 180},
  // {title: '更新人', dataIndex: 'updater', width: 100},
  {title: '操作', dataIndex: 'handle', width: 100, fixed: 'right'}
]
// 审查超过5分钟
// const isOverFiveMinute = (record: any)=> {
//   const createTime = new Date(record.createTime).getTime()
//   const currentTime = new Date().getTime()
//   return (currentTime - createTime) > 5 * 60 * 1000  && record.reviewStatus === 1 // 审查中超过5分钟
// }
//  操作
const dialogOpen = ref(false)
const batchDeleteMode = ref(false)

function goResult(record: any) {
  const {taskId} = record
  router.push({
    name: 'ComplianceReview',
    query: {taskId}
  })
}

async function onTableChange(pag: any) {
  loading.value = true
  const {current, pageSize} = pag
  const {fileName, reviewStatus, reviewResult} = form

  // 更新页面大小
  if (pageSize && pageSize !== defaultPageSize.value) {
    defaultPageSize.value = pageSize
    pagination.value.pageSize = pageSize
    // 页面大小变化时，通常回到第一页
    if (current === 1) {
      pagination.value.current = 1
    }
  }

  // 处理日期范围
  let startTime = ''
  let endTime = ''
  if (dateRange.value && dateRange.value.length === 2) {
    startTime = dateRange.value[0].format('YYYY-MM-DD')
    endTime = dateRange.value[1].format('YYYY-MM-DD')
  }

  const params = {
    pageNum: current,
    pageSize: pageSize || defaultPageSize.value,
    fileName,
    reviewStatus,
    reviewResult,
    startTime,
    endTime
  }

  const {data, err} = await apiTaskList(params)
  if (err) {
    loading.value = false
    return
  }
  const {total, dataList} = data
  pagination.value.current = current
  pagination.value.total = total
  tableData.value = dataList
  loading.value = false
}

//审查报告导出
const exportLoading = ref(false)
async function exportReport(record: any) {
  if (exportLoading.value) return
  exportLoading.value = true
  const {taskId} = record
  const params = {taskId}
  await apiReviewImport(params)
  exportLoading.value = false
}

// 防抖查询函数 - 使用 VueUse 的 useDebounceFn
const debouncedSearch = useDebounceFn(() => {
  getTableData()
  getDashboardData()
}, 500)

// 获取数据看板数据
async function getDashboardData() {
    let startTime = ''
    let endTime = ''
    if (dateRange.value && dateRange.value.length === 2) {
      startTime = dateRange.value[0].format('YYYY-MM-DD')
      endTime = dateRange.value[1].format('YYYY-MM-DD')
    }
    const { data, err } = await taskDashboard({startTime,endTime})
    if (err)  return

    dashboardData.value = {
      totalCount: data.totalCount || 0,
      riskFoundCount: data.riskFoundCount || 0,
      noRiskCount: data.noRiskCount || 0,
      pendingCount: data.pendingCount || 0,
      errorCount: data.errorCount || 0
    }
}

// 文件名输入处理
function onFileNameInput() {
  debouncedSearch()
}

// 结果变化处理
function onResultChange(val: number) {
  form.reviewResult = undefined
  if(val===10) form.reviewResult = 0
  else if(val===11) form.reviewResult = 1
  getTableData()
}

// 日期范围变化处理
function onDateRangeChange() {
  debouncedSearch()
}

// 重置筛选条件
function resetFilters() {
  form = {
    fileName: '',
    reviewStatus: undefined,
    reviewResult: undefined
  }
  dateRange.value = undefined
  // 重置时保持当前页面大小
  pagination.value.current = 1
  getTableData()
}



function doDel(record: any, event?: Event) {
  // 定位Popover到触发按钮的位置
  if (event && deletePopoverTrigger.value) {
    const target = event.currentTarget as HTMLElement
    const rect = target.getBoundingClientRect()
    // 计算按钮中心位置
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    deletePopoverTrigger.value.style.position = 'fixed'
    deletePopoverTrigger.value.style.left = `${centerX + 20}px`
    deletePopoverTrigger.value.style.top = `${centerY}px`
    deletePopoverTrigger.value.style.visibility = 'visible'
  }

  dialogOpen.value = true
  batchDeleteMode.value = false
  record2Delete.value = record
}

// 批量删除
function doBatchDelete(event?: Event) {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的项目')
    return
  }

  // 定位Popover到批量删除按钮的位置
  if (event && deletePopoverTrigger.value) {
    const target = event.currentTarget as HTMLElement
    const rect = target.getBoundingClientRect()
    // 计算按钮中心位置
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    deletePopoverTrigger.value.style.position = 'fixed'
    deletePopoverTrigger.value.style.left = `${centerX}px`
    deletePopoverTrigger.value.style.top = `${centerY  + 20}px`
    deletePopoverTrigger.value.style.visibility = 'visible'
  }

  batchDeleteMode.value = true
  dialogOpen.value = true
}

async function doDialogOk() {
  dialogOpen.value = false

  if (batchDeleteMode.value) {
    // 批量删除模式
    const taskIds = selectedRowKeys.value.join(',')
    const params = {taskId: taskIds}

    const {err} = await apiTaskDelete(params)
    if (err) return

    message.success(`成功删除 ${selectedRowKeys.value.length} 个任务`)
    selectedRowKeys.value = []
    batchDeleteMode.value = false
  } else {
    // 单个删除模式
    const {taskId} = record2Delete.value
    const params = {taskId}

    const {err} = await apiTaskDelete(params)
    if (err) return

    message.success('删除成功')
  }

  await getTableData()
}

async function getTableData() {
  loading.value = true
  dialogOpen.value = false
  const {fileName, reviewStatus, reviewResult} = form

  // 处理日期范围
  let startTime = ''
  let endTime = ''
  if (dateRange.value && dateRange.value.length === 2) {
    startTime = dateRange.value[0].format('YYYY-MM-DD')
    endTime = dateRange.value[1].format('YYYY-MM-DD')
  }
  
  const params = {
    pageNum: 1,
    pageSize: pagination.value.pageSize || defaultPageSize.value,
    fileName,
    reviewStatus: reviewStatus === 10 || reviewStatus === 11? undefined : reviewStatus,
    reviewResult,
    startTime,
    endTime
  }

  const {data, err} = await apiTaskList(params)
  if (err) {
    message.error('数据加载失败')
    loading.value = false
    return
  }

  const {total, dataList} = data
  pagination.value.current = 1
  pagination.value.total = total
  tableData.value = dataList

  // 清空选择状态，避免选择的项目在新数据中不存在
  selectedRowKeys.value = []

  loading.value = false
}
// const timerId = ref()
const loading = ref(false)

//5分钟后，刷新列表和数据看板
// function timer() {
//   clearTimeout(timerId.value)
//   timerId.value = setTimeout(() => {
//     if (!loading.value) {
//       const current = pagination.value.current
//       onTableChange({ current })
//       // 同时刷新数据看板
//       getDashboardData()
//     }
//   }, 5 * 60 * 1000)
// }

onMounted(async () => {
  await Promise.all([
    getDashboardData(),
    getTableData()
  ])
  // timer()
})

onUnmounted(()=> {
  // clearTimeout(timerId.value)
  // timerId.value = null
})
</script>

<style lang="scss" scoped>
.library {
  height: calc(100vh - 48px);
  display: flex;
  flex-direction: column;
  padding:24px;

  // 审查结果列表样式
  .result-summary-section {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    .summary-header {
      font-size: 24px;
      margin-right: 30px;
    }

    .summary-stats-bar {
      display: flex;
      align-items: center;
      background: #F9FAFB;
      border: 1px solid #E5E7EB;
      box-sizing: border-box;
      border-radius: 8px;
      padding: 0 21px;
      height: 46px;

      .stat-group {
        display: flex;
        align-items: center;
        gap: 8px;  
        .stat-label {  
          color: #4B5563; 
        }

        .stat-number { 
          font-size: 18px; 

          &.total {
            color: var(--main-6);
          }

          &.safe {
            color: #52C41A;
          }

          &.risk {
            color: #F5222D;
          }

          &.pending {
            color: #FA8C16;
          }
        }
      }

      .stat-divider {
        width: 1px;
        height: 16px;
        background: #D1D5DB;
        margin: 0 24px;
      }
    }
  }
  // 筛选区域样式
  .filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .filter-left {
      flex: 1;
    }

    .filter-right {
      flex-shrink: 0;
    }

    .batch-operation {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 12px;
      border-radius: 8px; 

      .batch-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .batch-selected {
          font-size: 14px;
          color: #6B7280;
          white-space: nowrap;
          min-width: 80px;
          font-weight: 500;
        }

        .batch-hint {
          display: flex;
          align-items: center;

          .batch-icon {
            width: 16px;
            height: 16px;
            animation: fadeIn 0.3s ease;
          }
        }
      }
    }
    .search-input {
      .search-icon {
        color: #9CA3AF;
      }
    }
  }
  :deep(.ant-btn-dangerous.batch-delete-button){
    border-color:#D9D9D9 ;
  }
  :deep(.ant-btn) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.ant-modal-body) {
    max-height: 75vh;
    margin-top: 32px;
  }
  :deep(.ant-pagination) {
    position: relative;
    .ant-pagination-total-text {
      position: absolute;
      z-index: 1;
      left: 0;
    }
    .ant-pagination-item-active {
      background-color: var(--main-6);
      color: var(--text-0);
      a {
        color: var(--text-0);
      }
    }
  }
  :deep(.ant-table) {
    flex: 1;
    .ant-tag >.anticon+span,
    .ant-tag >span+.anticon {
      margin-left: 4px !important;
    } 
    .btns {
      display: flex;
      gap:16px;
      .ant-btn {
        width: min-content;
        border: none;
        box-shadow: none;
        padding:0;
        &:hover {
          background: transparent;
        }
      }
    }
    .ant-table-cell{
      color: #000;
    }
    th.ant-table-cell{
      font-weight: normal;
      color: #374151;
    }

    // 项目信息样式
    .project-info {
      .project-title { 
        font-size: 16px;
        color: #000; 
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .project-code { 
        color: #6B7280;
      }
    }

    // 状态Badge样式
    .status-badge {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      width: fit-content;

      .status-icon {
        width: 16px;
        height: 16px;
      }
    }
  }
}

// 删除确认Popover样式
:deep(.delete-confirm-popover) {
  .ant-popover-content {
    padding: 0;
  }

  .ant-popover-inner {
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
                0 3px 6px -4px rgba(0, 0, 0, 0.12),
                0 9px 28px 8px rgba(0, 0, 0, 0.05);
    min-width: 320px;
    max-width: 400px;
  }
}

.delete-confirm-content {
  .main-box {
    display: flex;
    margin-bottom: 8px; 
  }
  .warning-icon { 
    flex-shrink: 0;
    margin:4px 8px 0 0; 
  }

  .delete-message {  
    flex:1;
    min-width: 0;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.88); 
  }

  .delete-dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .ant-btn {   
      font-size: 12px;
      &[type="primary"] {
        &.ant-btn-dangerous {
          background-color: #F5222D;
          border-color: #F5222D; 
          &:hover {
            background-color: var(--error-5, #ff7875);
            border-color: var(--error-5, #ff7875);
          }
        }
      }
    }
  }
}
</style>
