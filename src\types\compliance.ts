/**
 * 合规性审查相关类型定义
 */

// 风险等级类型
export type RiskLevel = 'risk' | 'safe' | 'na'

// 筛选标签类型
export interface FilterTab {
  key: string
  label: string
  count: number
  color: string
}

// 审查项目类型
export interface ReviewItem {
  id: string
  title?: string
  description: string
  details?: string
  riskDetails?: string
  riskLevel: RiskLevel
  documentLink?: string
  pageNumber?: number
  subItemCount?: number
  legalBasis?: string
  suggestion?: string
  likeCount?: number
  dislikeCount?: number
  subItems?: SubReviewItem[]
  coordinates?: {
    x: number
    y: number
    width: number
    height: number
  }
  createdAt?: string
  updatedAt?: string
}

// 子审查项目类型
export interface SubReviewItem {
  id: string
  description: string
  riskLevel: RiskLevel
  riskDetails?: string
  legalBasis?: string
  suggestion?: string
  likeCount?: number
  dislikeCount?: number
  documentLink?: string
}

// 审查任务类型
export interface ReviewTask {
  id: string
  taskName: string
  fileName: string
  fileUrl: string
  fileId?: string
  reviewTime: string
  status: 'pending' | 'in_progress' | 'completed'
  totalItems: number
  riskItems: number
  safeItems: number
  naItems: number
  items: ReviewItem[]
}

// PDF高亮区域类型
export interface HighlightRect {
  page: number
  x1: number
  y1: number
  x2: number
  y2: number
  color?: string
  itemId?: string
}

// 导出配置类型
export interface ExportConfig {
  format: 'pdf' | 'word' | 'excel'
  includeDetails: boolean
  includeImages: boolean
  filterByRisk?: RiskLevel[]
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 审查清单项目类型
export interface CheckListItem {
  id: string
  category: string
  title: string
  description: string
  required: boolean
  completed: boolean
  riskLevel?: RiskLevel
  notes?: string
}

// 审查清单类型
export interface CheckList {
  id: string
  name: string
  version: string
  categories: string[]
  items: CheckListItem[]
  createdAt: string
  updatedAt: string
}
