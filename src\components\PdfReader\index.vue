<template>
  <div v-if="iframeSrc" class="pdf-reader">
    <iframe id="pdfViewer" ref="refIframe" width="100%" height="100%" :src="iframeSrc" @load="onLoad"/>
    <div v-if="loading" class="loading-wrapper">
      <div class="loading"></div>
    </div>
  </div>
</template>

<script setup lang='ts'>
import { computed, nextTick, ref } from 'vue'

defineOptions({
  name: 'PdfReader'
})
type Props = {
  url: string // 地址
  page?:number// 页码
  zoom?:number// 缩放比例
  search?:string// 高亮关键词
  replaceText?:string// 替换关键词
  rect?:any[] //  [{id,page,x1,y1,x2,y2,text}] 坐标点,text是要显示的文本
}
const props = withDefaults(defineProps<Props>(), {
  page: 1,
  zoom: 100,
  search:'',
  rect: ()=>[]
})


const srcPrefix = new URL('/static-resources/pdfjs-v5.4.54-dist/web/viewer.html?V1.0&file=', new URL(import.meta.url).origin).href
const iframeSrc = computed(()=> {
  const {url,  page = 1 ,  search ,rect, zoom = 100} = props
  if(!url) return ''
  // 使用本地代理路径，避免跨域问题，获取未编码过的地址
  let pathUrl = url
  if (url.startsWith('http')) {
    const protocolIndex = url.indexOf('://')
    const domainStartIndex = protocolIndex + 3
    const pathStartIndex = url.indexOf('/', domainStartIndex)
    if (pathStartIndex !== -1) {
      pathUrl = url.substring(pathStartIndex)
    }
  }  
  // const testRect = [{
  //     id: 'demo2',
  //     text: `二、《深圳经济特区政府采购条例实施细则》第七十五条 供应商有下列情形之一的，属于采购条`,
  //     page: 1,
  //     x1: 90,
  //     y1: 300,
  //     x2: 450,
  //     y2: 345,
  //   }]
  
  const encodedHighlights = encodeURIComponent(JSON.stringify(rect));
  const finalSrc = srcPrefix.concat(
    encodeURIComponent(pathUrl),
    `&zoom=${zoom}&search=${search}&highlights=${encodedHighlights}#page=${page}`
  )
  return finalSrc
})
const emits = defineEmits(['load'])
const loading = ref(true) 
const refIframe = ref<HTMLIFrameElement | null>(null)  
const onLoad = async () => { 
    setTimeout(()=> {
      loading.value = false 
      emits('load')
    }, 1000) 
        
    return
}
</script>

<style lang="scss" scoped>
.pdf-reader {
  width: 100%;
  height: 100%;
  position: relative;
}

.loading-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--fill-rbg-0), 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading {
  width: 40px;
  height: 40px;
  border: 3px solid var(--fill-1);
  border-top: 3px solid var(--main-6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

