<template>
  <div v-if="iframeSrc" class="pdf-reader">
    <iframe id="pdfViewer" ref="refIframe" width="100%" height="100%" :src="iframeSrc" @load="onLoad"/>
    <div v-if="loading" class="loading-wrapper">
      <div class="loading"></div>
    </div>
  </div>
</template>

<script setup lang='ts'>
import { computed, nextTick, ref } from 'vue'

defineOptions({
  name: 'PdfReader'
})
type Props = {
  url: string // 地址
  page?:number// 页码
  zoom?:number// 缩放比例
  search?:string// 高亮关键词
  rect?:any[] //  [{page,x1,y1,x2,y2}] 坐标点
}
const props = withDefaults(defineProps<Props>(), {
  page: 1,
  zoom: 100,
  search:'',
  rect: ()=>[]
})


const srcPrefix = new URL('/static-resources/pdfjs-v5.4.54-dist/web/viewer.html?V1.3&file=', new URL(import.meta.url).origin).href

const iframeSrc = computed(()=> {
  const {url,  page = 1 ,  search ,rect, zoom = 100} = props
  if(!url) return ''

  let pathUrl = url

  // 处理完整的HTTP URL
  if (url.startsWith('http')) {
    try {
      const urlObj = new URL(url)

      // 检查是否是需要代理的域名
      if (urlObj.hostname === 'sppgpttest.gcycloud.cn') {
        // 使用代理路径，保持原始路径结构
        pathUrl = urlObj.pathname + (urlObj.search || '')
        console.log('Using proxy path:', pathUrl)
      } else {
        // 对于其他域名，保持完整URL
        pathUrl = url
      }
    } catch (error) {
      console.error('URL parsing error:', error)
      pathUrl = url
    }
  }

  const encodedHighlights = encodeURIComponent(JSON.stringify(rect));
  console.log('Original URL:', url)
  console.log('Processed pathUrl:', pathUrl)

  // 正确处理URL编码
  // 对于包含中文字符的文件名，需要特殊处理
  let encodedPath
  try {
    // 检查路径是否已经编码
    const testDecode = decodeURIComponent(pathUrl)
    if (testDecode !== pathUrl) {
      // 已经编码过，直接使用
      encodedPath = pathUrl
    } else {
      // 未编码，进行编码
      encodedPath = encodeURIComponent(pathUrl)
    }
  } catch (e) {
    // 编码处理失败，尝试直接编码
    try {
      encodedPath = encodeURIComponent(pathUrl)
    } catch (e2) {
      // 如果还是失败，使用原始路径
      encodedPath = pathUrl
    }
  }

  const finalSrc = srcPrefix.concat(
    encodedPath,
    `&zoom=${zoom}&search=${search}&highlights=${encodedHighlights}#page=${page}`
  )

  console.log('Final PDF viewer URL:', finalSrc)
  return finalSrc
})
const emits = defineEmits(['load'])
const loading = ref(true) 
const refIframe = ref<HTMLIFrameElement | null>(null) 
const onLoad = async () => { 
    setTimeout(()=> {
      loading.value = false
      emits('load')
    }, 1000) 
        
    return
}
</script>

<style lang="scss" scoped>
.pdf-reader {
  width: 100%;
  height: 100%;
  position: relative;
}

.loading-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--fill-rbg-0), 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading {
  width: 40px;
  height: 40px;
  border: 3px solid var(--fill-1);
  border-top: 3px solid var(--main-6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

