<template>
  <div class="pdf-reader">
    <div v-if="error" class="error-wrapper">
      <div class="error-content">
        <h3>PDF加载失败</h3>
        <p>{{ error }}</p>
        <div class="debug-info">
          <p><strong>原始URL:</strong> {{ props.url }}</p>
          <p><strong>处理后URL:</strong> {{ processedUrl }}</p>
          <p><strong>最终iframe URL:</strong> {{ iframeSrc }}</p>
        </div>
        <a-button @click="retry">重试</a-button>
      </div>
    </div>
    <div v-else-if="iframeSrc" class="iframe-wrapper">
      <iframe
        id="pdfViewer"
        ref="refIframe"
        width="100%"
        height="100%"
        :src="iframeSrc"
        @load="onLoad"
        @error="onError"
      />
      <div v-if="loading" class="loading-wrapper">
        <div class="loading"></div>
        <p>正在加载PDF文档...</p>
      </div>
    </div>
    <div v-else class="placeholder">
      <a-empty description="请提供PDF URL" />
    </div>
  </div>
</template>

<script setup lang='ts'>
import { computed, nextTick, ref } from 'vue'

defineOptions({
  name: 'PdfReader'
})
type Props = {
  url: string // 地址
  page?:number// 页码
  zoom?:number// 缩放比例
  search?:string// 高亮关键词
  rect?:any[] //  [{page,x1,y1,x2,y2}] 坐标点
  forceProxy?: boolean // 强制使用代理
  disableProxy?: boolean // 禁用代理
}
const props = withDefaults(defineProps<Props>(), {
  page: 1,
  zoom: 100,
  search:'',
  rect: ()=>[],
  forceProxy: false,
  disableProxy: false
})


const srcPrefix = new URL('/static-resources/pdfjs-v5.4.54-dist/web/viewer.html?V1.3&file=', new URL(import.meta.url).origin).href

// 获取当前应用的基础URL，用于判断是否需要代理
const getCurrentOrigin = () => {
  return window.location.origin
}

// 检查URL是否需要通过代理访问
const needsProxy = (url: string, forceProxy: boolean, disableProxy: boolean) => {
  // 如果禁用代理，直接返回false
  if (disableProxy) return false

  // 如果强制使用代理，直接返回true
  if (forceProxy) return true

  if (!url.startsWith('http')) return false

  try {
    const urlObj = new URL(url)
    const currentOrigin = getCurrentOrigin()

    // 如果URL的域名与当前应用不同，且不是本地开发环境，则需要代理
    return urlObj.origin !== currentOrigin && !urlObj.hostname.includes('localhost')
  } catch (error) {
    console.error('URL parsing error:', error)
    return false
  }
}

const iframeSrc = computed(()=> {
  const {url, page = 1, search, rect, zoom = 100, forceProxy = false, disableProxy = false} = props
  if(!url) return ''

  let pathUrl = url

  // 处理完整的HTTP URL
  if (url.startsWith('http')) {
    try {
      const urlObj = new URL(url)

      // 检查是否需要代理
      if (needsProxy(url, forceProxy, disableProxy)) {
        // 使用代理路径，只保留路径部分
        pathUrl = urlObj.pathname + (urlObj.search || '')
        console.log('Using proxy path:', pathUrl)
      } else {
        // 同域名或本地环境，保持完整URL
        pathUrl = url
      }
    } catch (error) {
      console.error('URL parsing error:', error)
      pathUrl = url
    }
  }

  const encodedHighlights = encodeURIComponent(JSON.stringify(rect));
  console.log('Original URL:', url)
  console.log('Processed pathUrl:', pathUrl)

  // 正确处理URL编码
  // 对于包含中文字符的文件名，需要特殊处理
  let encodedPath
  try {
    // 检查路径是否已经编码
    const testDecode = decodeURIComponent(pathUrl)
    if (testDecode !== pathUrl) {
      // 已经编码过，直接使用
      encodedPath = pathUrl
    } else {
      // 未编码，进行编码
      encodedPath = encodeURIComponent(pathUrl)
    }
  } catch (e) {
    // 编码处理失败，尝试直接编码
    try {
      encodedPath = encodeURIComponent(pathUrl)
    } catch (e2) {
      // 如果还是失败，使用原始路径
      encodedPath = pathUrl
    }
  }

  const finalSrc = srcPrefix.concat(
    encodedPath,
    `&zoom=${zoom}&search=${search}&highlights=${encodedHighlights}#page=${page}`
  )

  // 更新processedUrl用于调试显示
  processedUrl.value = pathUrl

  console.log('Final PDF viewer URL:', finalSrc)
  return finalSrc
})
const emits = defineEmits(['load'])
const loading = ref(true)
const error = ref('')
const processedUrl = ref('')
const refIframe = ref<HTMLIFrameElement | null>(null)
const onLoad = async () => {
    setTimeout(()=> {
      loading.value = false
      error.value = ''
      emits('load')
    }, 1000)

    return
}

const onError = () => {
  loading.value = false
  error.value = 'PDF文档加载失败，请检查URL是否正确或网络连接'
}

const retry = () => {
  error.value = ''
  loading.value = true
}
</script>

<style lang="scss" scoped>
.pdf-reader {
  width: 100%;
  height: 100%;
  position: relative;
}

.error-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;

  .error-content {
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    text-align: center;

    h3 {
      color: #ff4d4f;
      margin-bottom: 16px;
    }

    .debug-info {
      background: #f8f8f8;
      padding: 12px;
      border-radius: 4px;
      margin: 16px 0;
      text-align: left;
      font-size: 12px;

      p {
        margin: 4px 0;
        word-break: break-all;
      }
    }
  }
}

.iframe-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--fill-rbg-0), 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading {
  width: 40px;
  height: 40px;
  border: 3px solid var(--fill-1);
  border-top: 3px solid var(--main-6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

