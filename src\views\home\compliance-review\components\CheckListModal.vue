<template>
  <BaseDrawer
    v-model="visible"
    title="审查清单"
    :width="640"
    :loading="loading"
    :ok-visible="true"
    :cancel-visible="false"
    :ok-loading="saving"
    ok-text="重新审查"
    @ok="handleSave"
    @cancel="handleClose"
  >
    <div class="checklist-content"> 
      <div class="form-section">
        <div class="form-label">审查方式：</div>
        <a-select
          v-model:value="reviewMethod"
          placeholder="请选择审查方式"
          style="width: 320px"
          :options="reviewMethodOptions"
        />
      </div>
      <div class="form-section">
        <div class="form-label">审查清单：</div>
        <a-select
          v-model:value="reviewContent"
          placeholder="请选择审查清单"
          style="width: 320px"
          :options="reviewContentOptions"
        />
      </div>
      <div class="form-section">
        <div class="form-label">审查项：</div>
      </div>

      <!-- 审查项目列表 -->
      <div class="checklist-items">
        <a-checkbox-group
          v-model:value="selectedCheckPoints"
          class="checkbox-group"
        >
          <!-- 按分类显示审查项目 -->
          <div
            v-for="category in groupedCheckPoints"
            :key="category.code"
            class="category-section"
          >
            <h4 class="category-title">{{ category.title }}</h4>
            <div class="category-items">
              <div
                v-for="checkPoint in category.items"
                :key="checkPoint.pointId"
                class="checklist-item"
              >
                <a-checkbox
                  :value="checkPoint.pointId"
                  class="checkbox-item"
                >
                  <div class="point-name">{{ checkPoint.pointName }}</div>
                </a-checkbox>
              </div>
            </div>
          </div>
        </a-checkbox-group>

        <!-- 空状态 -->
        <div v-if="checkPointList.length === 0 && !loading" class="empty-state">
          <a-empty description="暂无审查项目" />
        </div>
      </div>
    </div>
  </BaseDrawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue' 
import BaseDrawer from '@/components/BaseDrawer/base-drawer.vue'
import { apiGetCheckPointPage } from '@/api/examine'
import { getReviewInfo } from '@/views/home/<USER>/examine'

defineOptions({
  name: 'CheckListModal'
})

// 审查点数据类型
interface CheckPoint {
  reviewItemCode: string
  pointId: string
  pointName: string
  basisDesc: string
  status: number
  createTime: string
}

interface Props {
  open: boolean
  taskId: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:open': [value: boolean]
  'save': [data: any]
}>()

// 响应式状态
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const loading = ref(false)
const saving = ref(false)

// 审查方式和清单选项
const reviewMethod = ref('公开招标')
const reviewContent = ref('深圳政府采购公开招标文件审查清单')

const reviewMethodOptions = [
  { label: '公开招标', value: '公开招标' },
  { label: '邀请招标', value: '邀请招标' },
  { label: '竞争性谈判', value: '竞争性谈判' },
  { label: '单一来源', value: '单一来源' }
]

const reviewContentOptions = [
  { label: '深圳政府采购公开招标文件审查清单', value: '深圳政府采购公开招标文件审查清单' },
  { label: '深圳政府采购邀请招标文件审查清单', value: '深圳政府采购邀请招标文件审查清单' }
]

// 审查点数据
const checkPointList = ref<CheckPoint[]>([])
const selectedCheckPoints = ref<string[]>([]) // 选中的审查点ID列表

// 按reviewItemCode分组的审查项目
const groupedCheckPoints = computed(() => {
  const groups: { [key: string]: CheckPoint[] } = {}

  // 按reviewItemCode分组
  checkPointList.value.forEach(item => {
    const code = item.reviewItemCode || 'other'
    if (!groups[code]) {
      groups[code] = []
    }
    groups[code].push(item)
  })

  // 转换为数组格式，并添加分类标题
  return Object.keys(groups).map(code => {
    const reviewInfo = getReviewInfo(code)
    return {
      code,
      title: reviewInfo?.text || '其他',
      items: groups[code]
    }
  })
})

// 方法

const handleClose = () => {
  visible.value = false
}

const handleSave = async () => { 
    saving.value = true 
    emit('save','') 
}

// 加载审查点数据
const loadCheckPoints = async () => {
    loading.value = true
    try {
      // 获取所有分类的审查点数据
      const reviewItemCodes = ['qual_fairness', 'req_fairness', 'rule_fairness']
      const allCheckPoints: CheckPoint[] = []

      for (const code of reviewItemCodes) {
        const requestData = {
          pageNum: 1,
          pageSize: 100,
          reviewItemCode: code
        }
        const {data, err} = await apiGetCheckPointPage(requestData)
        if (err) return
        const dataList = data?.dataList || []
        allCheckPoints.push(...dataList)
      }

      checkPointList.value = allCheckPoints
      // 默认全选
      selectedCheckPoints.value = checkPointList.value.map(item => item.pointId)
    } catch (error) {
      console.error('加载审查点数据失败:', error)
    } finally {
      loading.value = false
    }
}

// 监听抽屉打开状态
watch(() => props.open, (newVal) => {
  if (newVal) {
    loadCheckPoints()
  }
})
</script>

<style lang="scss" scoped>
.checklist-content {
  padding: 24px;

  .form-section {
    display: flex;
    align-items: center;
    margin-bottom: 16px; 
  }

  .checklist-items {
    .checkbox-group {
      display: flex;
      flex-direction: column; 
    }

    .category-section {
      .category-title { 
        font-weight: 600;  
        padding: 8px;
        color: #000000E0;
        background-color: #00000005;
        border-bottom: 1px solid #F0F0F0;
      } 
    }

    .checklist-item {
      padding:12px 0;  
      border-bottom:1px solid #F0F0F0;
      .point-name {
        white-space: break-spaces;
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 0;
      color: var(--text-3, #999);
    }
  } 
} 
</style>
