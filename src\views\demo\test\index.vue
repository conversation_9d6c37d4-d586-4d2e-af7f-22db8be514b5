<template>
  <div class="compliance-review-page">
    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div
        v-for="card in statsCards"
        :key="card.id"
        class="stats-card"
      >
        <div   class="stats-title">{{ card.title }}</div>
        <div class="stats-info">
          <div class="stats-number" :class="card.numberClass">{{ card.number }}</div>
          <div v-if="card.badge" class="real-time-badge">
            <span>{{ card.badge }}</span>
            </div>
          <div v-else class="stats-compare">
            <span class="compare-text">较上周期</span>
            <div :class="card.trendClass">
              <component :is="card.trendIcon" :size="12" class="trend-icon" />
              <span class="trend-text">{{ card.trendText }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和操作区域 -->
    <div class="filter-section">
      <div class="filter-left">
        <a-select
          v-model:value="filters.status"
          placeholder="审查状态"
          style="width: 120px"
          allow-clear
        >
          <a-select-option value="pending">处理中</a-select-option>
          <a-select-option value="reviewing">审查中</a-select-option>
          <a-select-option value="completed">已完成</a-select-option>
        </a-select>

        <a-select
          v-model:value="filters.result"
          placeholder="审查结果"
          style="width: 120px"
          allow-clear
        >
          <a-select-option value="safe">未发现风险</a-select-option>
          <a-select-option value="risk">发现风险</a-select-option>
        </a-select>

        <a-date-picker
          v-model:value="filters.startDate"
          placeholder="开始日期"
          style="width: 140px"
        >
          <template #suffixIcon>
            <Calendar :size="16" />
          </template>
        </a-date-picker>

        <span class="date-separator">至</span>

        <a-date-picker
          v-model:value="filters.endDate"
          placeholder="结束日期"
          style="width: 140px"
        >
          <template #suffixIcon>
            <Calendar :size="16" />
          </template>
        </a-date-picker>

        <a-button @click="resetFilters">
          <template #icon>
            <RefreshCw :size="16" />
          </template>
          重置
        </a-button>

        <a-input
          v-model:value="filters.fileName"
          placeholder="搜索文件名称"
          style="width: 200px"
          allow-clear
        >
          <template #suffix>
            <Search :size="16" />
          </template>
        </a-input>
      </div>

      <div class="filter-right">
        <span class="selected-count">已选择 {{ selectedRowKeys.length }} 项</span>
        <a-button :disabled="selectedRowKeys.length === 0">
          <template #icon>
            <Settings :size="16" />
          </template>
          批量操作
        </a-button>

        <a-button type="primary">
          <template #icon>
            <Plus :size="16" />
          </template>
          新建审查任务
        </a-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :row-selection="rowSelection"
        :pagination="paginationConfig"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'projectInfo'">
            <div class="project-info">
              <div class="project-name">{{ record.projectName }}</div>
              <div class="project-code">{{ record.projectCode }}</div>
            </div>
          </template>

          <template v-if="column.key === 'reviewResult'">
            <a-tag
              :color="getResultTagColor(record.reviewResult)"
              class="result-tag"
            >
              <template #icon>
                <component :is="getResultIcon(record.reviewResult)" :size="12" />
              </template>
              {{ getResultText(record.reviewResult) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'action'">
            <div class="action-buttons">
              <a-button type="text" size="small" @click="viewDetail(record)">
                <template #icon>
                  <Eye :size="16" />
                </template>
              </a-button>

              <a-button type="text" size="small" @click="editRecord(record)">
                <template #icon>
                  <Edit3 :size="16" />
                </template>
              </a-button>

              <a-button type="text" size="small" danger @click="deleteRecord(record)">
                <template #icon>
                  <Trash2 :size="16" />
                </template>
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  Calendar,
  RefreshCw,
  Search,
  Settings,
  Plus,
  Eye,
  Edit3,
  Trash2,
  CheckCircle2,
  AlertTriangle,
  Loader,
  Clock,
  TrendingUp,
  TrendingDown
} from 'lucide-vue-next'

// 接口类型定义
interface ReviewRecord {
  id: string
  projectName: string
  projectCode: string
  fileName: string
  reviewResult: 'safe' | 'risk' | 'reviewing' | 'pending'
  creator: string
  createTime: string
}

interface Filters {
  status?: string
  result?: string
  startDate?: Dayjs
  endDate?: Dayjs
  fileName?: string
}

// 响应式数据
const loading = ref(false)
const selectedRowKeys = ref<string[]>([])
const filters = reactive<Filters>({})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '采购项目名称/编号',
    key: 'projectInfo',
    width: 300,
    ellipsis: true
  },
  {
    title: '文件名称',
    dataIndex: 'fileName',
    width: 300,
    ellipsis: true
  },
  {
    title: '审查结果',
    key: 'reviewResult',
     width: 100
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    align: 'center',
    fixed: 'right'
  }
]

// 模拟数据
const mockData: ReviewRecord[] = [
  {
    id: '1',
    projectName: '深圳市宝安区人民医院蜂巢抗衰祛斑仪采购',
    projectCode: 'BACG2025000104-A',
    fileName: '[BACG2025000104-A]深圳市宝安区人民医院蜂巢抗衰…',
    reviewResult: 'safe',
    creator: '张三',
    createTime: '2024-01-15 09:30:00'
  },
  {
    id: '2',
    projectName: '深圳市宝安区中心医院医疗设备采购项目',
    projectCode: 'BACG2025000105-B',
    fileName: '[BACG2025000105-B]深圳市宝安区中心医院医疗设备…',
    reviewResult: 'reviewing',
    creator: '李四',
    createTime: '2024-01-14 14:20:00'
  },
  {
    id: '3',
    projectName: '宝安区人民医院手术器械采购招标文件',
    projectCode: 'BACG2025000106-C',
    fileName: '[BACG2025000106-C]宝安区人民医院手术器械采购招…',
    reviewResult: 'risk',
    creator: '王五',
    createTime: '2024-01-13 11:45:00'
  },
  {
    id: '4',
    projectName: '宝安区教育局办公设备采购项目',
    projectCode: 'BACG2025000107-D',
    fileName: '[BACG2025000107-D]宝安区教育局办公设备采购项目',
    reviewResult: 'pending',
    creator: '赵六',
    createTime: '2024-01-12 16:30:00'
  },
  {
    id: '5',
    projectName: '宝安区卫生健康局医疗器械采购',
    projectCode: 'BACG2025000108-E',
    fileName: '[BACG2025000108-E]宝安区卫生健康局医疗器械采购',
    reviewResult: 'safe',
    creator: '孙七',
    createTime: '2024-01-11 10:15:00'
  },
  {
    id: '6',
    projectName: '宝安区交通局车辆采购项目',
    projectCode: 'BACG2025000109-F',
    fileName: '[BACG2025000109-F]宝安区交通局车辆采购项目',
    reviewResult: 'reviewing',
    creator: '周八',
    createTime: '2024-01-10 15:20:00'
  },
  {
    id: '7',
    projectName: '宝安区环保局设备采购招标',
    projectCode: 'BACG2025000110-G',
    fileName: '[BACG2025000110-G]宝安区环保局设备采购招标',
    reviewResult: 'risk',
    creator: '吴九',
    createTime: '2024-01-09 09:45:00'
  },
  {
    id: '8',
    projectName: '宝安区文化局音响设备采购',
    projectCode: 'BACG2025000111-H',
    fileName: '[BACG2025000111-H]宝安区文化局音响设备采购',
    reviewResult: 'pending',
    creator: '郑十',
    createTime: '2024-01-08 14:30:00'
  },
  {
    id: '9',
    projectName: '宝安区体育局器材采购项目',
    projectCode: 'BACG2025000112-I',
    fileName: '[BACG2025000112-I]宝安区体育局器材采购项目',
    reviewResult: 'safe',
    creator: '王十一',
    createTime: '2024-01-07 11:20:00'
  },
  {
    id: '10',
    projectName: '宝安区科技局实验设备采购',
    projectCode: 'BACG2025000113-J',
    fileName: '[BACG2025000113-J]宝安区科技局实验设备采购',
    reviewResult: 'reviewing',
    creator: '李十二',
    createTime: '2024-01-06 16:15:00'
  },
  {
    id: '11',
    projectName: '宝安区民政局办公用品采购',
    projectCode: 'BACG2025000114-K',
    fileName: '[BACG2025000114-K]宝安区民政局办公用品采购',
    reviewResult: 'risk',
    creator: '张十三',
    createTime: '2024-01-05 13:40:00'
  },
  {
    id: '12',
    projectName: '宝安区住建局建材采购项目',
    projectCode: 'BACG2025000115-L',
    fileName: '[BACG2025000115-L]宝安区住建局建材采购项目',
    reviewResult: 'pending',
    creator: '刘十四',
    createTime: '2024-01-04 10:25:00'
  },
  {
    id: '13',
    projectName: '宝安区农业局农机设备采购',
    projectCode: 'BACG2025000116-M',
    fileName: '[BACG2025000116-M]宝安区农业局农机设备采购',
    reviewResult: 'safe',
    creator: '陈十五',
    createTime: '2024-01-03 08:50:00'
  },
  {
    id: '14',
    projectName: '宝安区水务局管道设备采购',
    projectCode: 'BACG2025000117-N',
    fileName: '[BACG2025000117-N]宝安区水务局管道设备采购',
    reviewResult: 'reviewing',
    creator: '杨十六',
    createTime: '2024-01-02 17:10:00'
  },
  {
    id: '15',
    projectName: '宝安区电力局电气设备采购',
    projectCode: 'BACG2025000118-O',
    fileName: '[BACG2025000118-O]宝安区电力局电气设备采购',
    reviewResult: 'risk',
    creator: '黄十七',
    createTime: '2024-01-01 12:35:00'
  }
]

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`,
  pageSizeOptions: ['10', '20', '50', '100']
})

// 表格数据
const dataSource = ref<ReviewRecord[]>([])

// 行选择配置
const rowSelection: TableProps['rowSelection'] = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}

// 工具函数
const getResultTagColor = (result: string) => {
  const colorMap = {
    safe: 'success',
    risk: 'error',
    reviewing: 'processing',
    pending: 'default'
  }
  return colorMap[result as keyof typeof colorMap] || 'default'
}

const getResultIcon = (result: string) => {
  const iconMap = {
    safe: CheckCircle2,
    risk: AlertTriangle,
    reviewing: Loader,
    pending: Clock
  }
  return iconMap[result as keyof typeof iconMap] || Clock
}

const getResultText = (result: string) => {
  const textMap = {
    safe: '未发现风险',
    risk: '发现风险',
    reviewing: '审查中',
    pending: '处理中'
  }
  return textMap[result as keyof typeof textMap] || '未知'
}

// 事件处理
const handleTableChange = (pagination: any, filters: any, sorter: any) => {
  paginationConfig.current = pagination.current
  paginationConfig.pageSize = pagination.pageSize
  loadData()
}

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    delete filters[key as keyof Filters]
  })
  loadData()
}

const viewDetail = (record: ReviewRecord) => {
  message.info(`查看详情: ${record.projectName}`)
}

const editRecord = (record: ReviewRecord) => {
  message.info(`编辑记录: ${record.projectName}`)
}

const deleteRecord = (record: ReviewRecord) => {
  message.info(`删除记录: ${record.projectName}`)
}

const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟分页数据
    const start = (paginationConfig.current - 1) * paginationConfig.pageSize
    const end = start + paginationConfig.pageSize

    dataSource.value = mockData.slice(start, end)
    paginationConfig.total = mockData.length
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadData()
})

// 统计卡片数据
const statsCards = ref([
  {
    id: 1,
    title: '审查总量',
    number: 12,
    numberClass: '',
    trendClass: 'trend-up',
    trendIcon: TrendingUp,
    trendText: '+12%'
  },
  {
    id: 2,
    title: '未发现风险文件',
    number: 4,
    numberClass: 'success',
    trendClass: 'trend-up',
    trendIcon: TrendingUp,
    trendText: '+8%'
  },
  {
    id: 3,
    title: '存在风险文件',
    number: 4,
    numberClass: 'danger',
    trendClass: 'trend-down',
    trendIcon: TrendingDown,
    trendText: '-5%'
  },
  {
    id: 4,
    title: '待处理任务',
    number: 4,
    numberClass: 'warning',
    trendClass: 'trend-up',
    trendIcon: TrendingUp,
    trendText: '+2%',
    badge: '实时数据'
  }
])
</script>

<style scoped lang="scss">
.compliance-review-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

// 统计卡片区域
.stats-section {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.stats-card {
  flex: 1;
  background: #f9fafb;
  border-radius: 8px;
  padding: 24px;
  position: relative;
  height: 84px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .stats-title {
    color: #374151;
  }
  .stats-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  .stats-number {
    font-size: 32px;
    font-weight: 600;
    color: #133ce8;
    margin-bottom: 8px;
    line-height: 1.2;

    &.success {
      color: #16a34a;
    }

    &.danger {
      color: #dc2626;
    }

    &.warning {
      color: #ea580c;
    }
  }

  .stats-compare {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;

    .compare-text {
      font-size: 12px;
      color: #6b7280;
      font-weight: 400;
    }

    .trend-up, .trend-down {
      display: flex;
      align-items: center;
      gap: 2px;

      .trend-icon {
        width: 12px;
        height: 12px;
      }

      .trend-text {
        font-size: 12px;
        font-weight: 500;
      }
    }

    .trend-up {
      color: #16a34a;
    }

    .trend-down {
      color: #dc2626;
    }
  }

  .real-time-badge {
    background: #ffedd5;
    border-radius: 8px;
    padding: 4px 8px;
    display: inline-block;
    margin-top: auto;
    width: fit-content;

    span {
      font-size: 12px;
      color: #ea580c;
      font-weight: 400;
    }
  }
}

// 筛选区域
.filter-section {
  background: white;
  border-radius: 8px;
  padding: 16px 24px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 2px -1px rgba(0, 0, 0, 0.1), 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.filter-left {
  display: flex;
  align-items: center;
  gap: 16px;

  .date-separator {
    color: #6b7280;
    font-size: 14px;
  }
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 16px;

  .selected-count {
    font-size: 14px;
    color: #4b5563;
  }
}

// 表格区域
.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px -1px rgba(0, 0, 0, 0.1), 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

// 表格内容样式
.project-info {
  .project-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
    font-size: 16px;
  }

  .project-code {
    font-size: 13px;
    color: #6b7280;
  }
}

.result-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  white-space: nowrap;
  min-width: fit-content;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;

  .ant-btn {
    border: none;
    box-shadow: none;

    &:hover {
      background-color: #f3f4f6;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .stats-section {
    flex-wrap: wrap;

    .stats-card {
      min-width: calc(50% - 12px);
      height: auto;
      min-height: 84px;
    }
  }

  .filter-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;

    .filter-left, .filter-right {
      justify-content: flex-start;
      flex-wrap: wrap;
    }
  }
}

@media (max-width: 768px) {
  .compliance-review-page {
    padding: 16px;
  }

  .stats-section {
    .stats-card {
      min-width: 100%;
      height: auto;
      min-height: 84px;
    }
  }

  .filter-left {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-right {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}

// Ant Design 组件样式覆盖
:deep(.ant-table) {
  .ant-table-thead > tr > th {
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 500;
    color: #374151;
  }

  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #e5e7eb;
    padding: 16px;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f9fafb;
  }
}

:deep(.ant-pagination) {
  margin: 16px 0;
  text-align: right;

  .ant-pagination-total-text {
    margin-right: auto;
  }
}

:deep(.ant-select) {
  .ant-select-selector {
    border-color: #d1d5db;

    &:hover {
      border-color: #9ca3af;
    }
  }
}

:deep(.ant-input) {
  border-color: #d1d5db;

  &:hover {
    border-color: #9ca3af;
  }

  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
}
:deep(.ant-btn) {
    display: flex;
    align-items: center;
    svg {
        margin-right: 4px;
    }
}
:deep(.ant-btn-primary) {
  background-color: #3b82f6;
  border-color: #3b82f6;

  &:hover {
    background-color: #2563eb;
    border-color: #2563eb;
  }
}
</style>
