<template>
  <div class="compliance-review-container">
    <!-- 顶部导航区域 -->
    <div class="header-section">
      <div class="breadcrumb-area">
        <div class="nav-buttons">
          <a-button type="text" class="nav-btn back-btn" @click="goHome">
            <template #icon>
              <CornerUpLeft class="icon" :size="16"/>
            </template>
            首页
          </a-button>
          <a-button type="text" class="nav-btn history-btn"@click="methods.showHistoryFiles">
            <template #icon>
              <Clock8 class="icon" :size="16"/>
            </template>
          </a-button>
        </div>
        <div class="file-name">{{ resultData.fileName }}</div>
      </div>
      
      <div class="info-actions">
        <div class="review-time">
          <Calendar1  class="icon" :size="16"/>
          <span>审查时间：{{ resultData.reviewTime }}</span>
        </div>
        <div class="action-buttons">
          <a-dropdown
            v-model:open="state.exportDropdownVisible"
            :trigger="['click']"
            placement="bottomRight"
          >
            <a-button class="export-btn">
              <template #icon>
                <Download class="icon" :size="16"/>
              </template>
              导出
              <DownOutlined />
            </a-button>
            <template #overlay>
              <div class="export-dropdown-content">
                <div class="export-options">
                  <div class="export-option">
                    <a-checkbox v-model:checked="state.exportOptions.original">
                      采购文件(原始)
                    </a-checkbox>
                  </div>
                  <div class="export-option">
                    <a-checkbox v-model:checked="state.exportOptions.annotated">
                      采购文件(修订版)
                    </a-checkbox>
                  </div>
                  <div class="export-option">
                    <a-checkbox v-model:checked="state.exportOptions.report">
                      审查风险报告
                    </a-checkbox>
                  </div>
                </div>
                <div class="export-actions">
                  <a-button size="small" @click="methods.cancelExport">取消</a-button>
                  <a-button
                    type="primary"
                    size="small"
                    :loading="state.exportLoading"
                    :disabled="!methods.hasSelectedOptions()"
                    @click="methods.confirmExport"
                  >
                    导出
                  </a-button>
                </div>
              </div>
            </template>
          </a-dropdown>
          <a-button type="primary" @click="methods.showCheckList">
            查看审查清单
          </a-button>
        </div>
      </div>
    </div>
    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- PDF阅读器区域 -->  
      <div class="pdf-reader-wrapper">
        <template v-if="pdfData.pdfUrl"> 
          <PdfReader
            v-if="isPdf"
            :url="pdfData.pdfUrl"
            :page="pdfData.currentPage" 
            :rect="pdfData.highlightRects" 
          />
          <KkfileReader v-else :url="pdfData.pdfUrl"/>  
        </template>
        <div v-else class="pdf-placeholder">
          <BaseEmpty description="暂无PDF文档" />
        </div>
      </div> 

      <!-- 审查结果面板 -->
      <div class="review-panel" ref="review-panel">
        <!--  已处理         --> 
        <div class="handle-result">
          已处理 <span class="num">{{resultData.resultFinishNum}}/{{ resultData.stats?.resultNum }}</span> 风险点
        </div>
        <div class="panel-header">审查结果</div> 
        <!-- 筛选标签 - 加载时显示骨架 -->
        <div class="filter-tabs">
          <!-- 加载状态显示骨架tab -->
          <template v-if="state.loading">
            <div
              v-for="tab in SKELETON_CONFIG.tabs"
              :key="tab.key"
              :class="['filter-tab', 'skeleton-tab', { active: tab.key === 'all' }]"
            >
              <span class="tab-label">{{ tab.label }}</span>
              <span class="tab-count skeleton-count">0</span>
            </div>
          </template>
          <!-- 正常状态显示真实tab -->
          <template v-else>
            <div
              v-for="tab in filterTabs"
              :key="tab.key +'1'"
              :class="['filter-tab', tab.key, { active: state.activeFilter === tab.key }]"
              @click="methods.setActiveFilter(tab.key)"
            >
              <span class="tab-label">{{ tab.label }}</span>
              <span class="tab-count" :class="[{ active: state.activeFilter === tab.key }]">{{ tab.count  || 0}}</span>
            </div>
          </template>
        </div>

        <!-- 审查项目列表 -->
        <div class="review-items">
          <a-spin :spinning="state.loading">
            <!-- 加载状态显示骨架屏 -->
            <div v-if="filteredItems?.length === 0" class="skeleton-container">
              <div
                v-for="(category, index) in SKELETON_CONFIG.categories"
                :key="index"
                class="skeleton-item-group"
              >
                <div class="skeleton-title-bar">
                  <span class="item-index"></span>
                  <span class="item-title">{{ category.name }}</span>
                </div>
                <div class="skeleton-content">
                  <div
                    v-for="n in category.itemCount"
                    :key="n"
                    class="skeleton-review-item"
                  >
                    <div class="skeleton-line skeleton-line-long"></div>
                    <div class="skeleton-line skeleton-line-medium"></div>
                    <div class="skeleton-line skeleton-line-short"></div>
                  </div>
                </div>
              </div>
            </div> 
            <!-- 正常内容 -->
            <div v-else class="items-list">
              <!-- 项目标题栏 -->
              <div
                v-for="item in filteredItems"
                :key="item.id"
                class="item-group"
              >
                <div class="item-title-bar">
                  <span class="item-index"></span>
                  <span class="item-title">{{ item.reviewItemName }}</span>
                  <span class="item-count">{{ item.pointNum || item.children?.length ||  0 }}</span>
                </div>
                <!-- 审查项目列表 -->
                <div class="sub-items"> 
                  <ReviewItem
                    v-for="subItem in item.children || []"
                    :key="subItem.uniqueId"
                    :data="subItem"
                    :active="activeItem.uniqueId" 
                    :task-id="taskId"
                    @updateFinishNum="val=> {resultData.resultFinishNum +=val}"
                    @clickItem="handleReviewItemClick"
                    @update:data="val => Object.assign(subItem, val)"
                  />
                </div>
              </div>
            </div>
          </a-spin>
        </div>
      </div>
    </div>

    <!-- 审查清单弹窗 -->
    <CheckListModal
      v-model:open="state.checkListVisible"
      :task-id="taskId"
      @save="methods.handleCheckListSave"
    />

    <!-- 历史文件弹窗 -->
    <HistoryFilesModal
      v-model="state.historyFilesVisible"
      :task-id="taskId"
      placement="left"
      @preview="methods.handleFilePreview"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, nextTick, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { CornerUpLeft, Clock8, Calendar1, Download } from 'lucide-vue-next'
import PdfReader from '@/components/PdfReader/index.vue'
import BaseEmpty from '@/components/BaseEmpty/index.vue'
import CheckListModal from './components/CheckListModal.vue'
import HistoryFilesModal from './components/HistoryFilesModal.vue'
import ReviewItem from './components/ReviewItem.vue' 
import KkfileReader from '@/components/KkfileReader/index.vue'
import { apiReviewImport, commentedFile, apiDownload } from '@/api/download'
import { getTaskReview, apiGetFile } from '@/api/examine'

defineOptions({
  name: 'ComplianceReview'
})

const router = useRouter()
const route = useRoute()

// 获取任务ID
const taskId = ref(route.query.taskId as string || '')

// 页面状态
const state = reactive<Record<string,any>>({
  loading: false,
  activeFilter: null,
  checkListVisible: false,
  historyFilesVisible: false,
  exportDropdownVisible: false,
  exportLoading: false,
  exportOptions: {
    original: false,
    annotated: false,
    report: false
  }
})

// 数据状态
const defaultResult = {
  fileId: '',
  finalFileId: '',// 显示文件ID
  fileName: '', // 文件名称
  reviewTime: '', // 审核时间
  dataList: [], // 数据列表
  stats: [], // 统计信息 
  resultFinishNum: 0,
  reviewResult: undefined, 
}
const isPdf = computed(() => {
  if (!pdfData.pdfUrl) return false
  return pdfData.pdfUrl.toLowerCase().endsWith('.pdf')
})
const pdfData = reactive<Record<string, any>>({
  pdfUrl: '',
  currentPage: 1 ,
  highlightRects: []
})
const highlightRects = computed(()=>{ 
  const dataList = resultData.dataList ?? []
  return dataList
    .filter(item => item.position && item.result === 1) // 过滤出有 position 的数据
    .flatMap(item => 
      item.position.map(pos => ({
        page: pos.page,
        x1: pos.x1,
        x2: pos.x2,
        y1: pos.y1,
        y2: pos.y2,
        text: item.updateStatus === 1 ? item.revisionSuggestion || '' : '',
        id: item.uniqueId
      }))
    )
})
// 点击审查项
const activeItem = ref<Record<string,any>>({}) // 选中的
const handleReviewItemClick = (item: any) => { 
  activeItem.value = item ?? {}
  console.log(item,'active')
  // pdfData.currentPage = -1
  nextTick(() => {
    pdfData.currentPage = item.page 
    pdfData.highlightRects =  item?.position?.map(pos => ({
        page: pos.page,
        x1: pos.x1,
        x2: pos.x2,
        y1: pos.y1,
        y2: pos.y2,
        // text: item.handleStatus === 1 ? item.revisionSuggestion || '' : '',
        id: item.uniqueId
      })) ?? []
      console.log(pdfData.highlightRects,'pdfData.highlightRects')
  })
}

const resultData = reactive<Record<string, any>>({ ...defaultResult })

const getData = async () => { 
  if(!taskId.value) {
    message.info('缺少任务ID')
    return
  }
  state.loading = true
  pdfData.pdfUrl = ''
  pdfData.currentPage = 1
  pdfData.highlightRects = []
  Object.assign(resultData, defaultResult)
  const { data, err } = await getTaskReview({  taskId: taskId.value, reviewResult: state.activeFilter })
  state.loading = false
  Object.assign(resultData, defaultResult,data,{resultFinishNum: data.stat?.resultFinishNum || 0})
}
// 骨架屏配置 - 使用常量避免响应式开销
const SKELETON_CONFIG = {
  categories: [
    { name: '资格公平性检查', itemCount: 3 },
    { name: '需求公平、合理性检查', itemCount: 4 },
    { name: '评审规则公平、合理性检查', itemCount: 2 }
  ],
  tabs: [
    { key: 'all', label: '全部' },
    { key: 'risk', label: '发现风险' },
    { key: 'safe', label: '未发现风险' },
    { key: 'na', label: '不适用' }
  ]
}

// 统计信息 
const filterTabs = computed(() => { 
  return [
    { key: null, label: '全部', count: resultData.stats?.sceneNum || 0},
    { key: 1, label: '发现风险', count: resultData.stats?.sceneFailureNum || 0},
    { key: 0, label: '未发现风险', count: resultData.stats?.sceneSuccessNum  || 0 },
    { key: 2, label: '不适用', count: resultData.stats?.noUseSceneNum || 0}
  ]
})

// 过滤 
const filteredItems = computed(() => { 
  let categorized = resultData.dataList 
  // 按 reviewItemCode 分类
  categorized = categorized.reduce((acc, item) => {
    let group = acc.find(group => group.reviewItemCode === item.reviewItemCode);
    if (!group) {
      group = {
        reviewItemCode: item.reviewItemCode,
        reviewItemName: item.reviewItemName,
        children: []
      };
      acc.push(group);
    }
    group.children.push(item);
    return acc;
  }, [] as { reviewItemCode: string; reviewItemName: string; children: any[] }[]);  
  return categorized;
})
// 导航方法
const goHome = () => {
  router.push({ name: 'HomeIndex' })
}  
// 简化的方法集合
const methods = {
  // 筛选方法
  setActiveFilter: (filterKey: number | null) => {
    state.activeFilter = filterKey 
    getData()
  }, 
  // PDF相关
  onPdfLoad: () => {
    console.log('PDF加载完成')
  },

  // 导出相关方法
  hasSelectedOptions: () => {
    const options = state.exportOptions
    return options.original || options.annotated || options.report
  },

  cancelExport: () => {
    state.exportDropdownVisible = false
  },

  confirmExport: async () => {
    try {
      const options = state.exportOptions
      const params = { taskId: taskId.value }
      const exportPromises = []
      state.exportLoading = true
      if (options.original) {
        exportPromises.push(apiDownload(resultData.fileId))
      }
      if (options.annotated) {
        exportPromises.push(commentedFile(params))
      }
      if (options.report) {
        exportPromises.push(apiReviewImport(params))
      }

      if (exportPromises.length > 0) {
        await Promise.all(exportPromises) 
        state.exportLoading = false
        state.exportDropdownVisible = false
      }
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  },

  // 弹窗控制
  showCheckList: () => {
    state.checkListVisible = true
  },

  showHistoryFiles: () => {
    state.historyFilesVisible = true
  },

  // 文件预览
  handleFilePreview: (file: any) => {
    taskId.value = file.taskId
    init()
  },

  // 审查清单保存
  handleCheckListSave: (checkList: any) => {
    console.log('保存审查清单:', checkList)
    message.success('审查清单已保存')
  }
} 
// 获取文件
const getFile = async ()=> {
  if(!resultData.finalFileId) {
    message.info('缺少文件ID')
    return
  }
  const {data, err} = await apiGetFile(resultData.finalFileId)
  if (err) return
  pdfData.pdfUrl = data.fileUrl
}
const init = async () => { 
  await getData()
  await getFile()
}
init()
</script>

<style lang="scss" scoped>
.compliance-review-container {
  color: #111827;
  height: 100vh;
  overflow:hidden;
  display:flex;
  flex-direction:column;
}

.header-section {
  flex-shrink:0;
  display: flex;
  align-items: center;
  justify-content: space-between; 
  padding: 11px 24px;
  border-bottom: 1px solid var(--line-2); 
  box-sizing:border-box;
  .breadcrumb-area {
    display: flex;
    align-items: center;  
    .nav-buttons {
      display: flex;
      gap: 16px;
      .nav-btn {
        display: flex;
        align-items: center; 
        &.back-btn { 
          padding: 8px 16px;
          border: 1px solid var(--line-3);
          border-radius: 4px;
          .icon {
            margin-right: 8px;
          }
        }
        &.history-btn{
          &:hover {
            background-color: transparent;
          }
        }
      }
    } 
  }
  
  .info-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    .review-time {
      display: flex;
      align-items: center;
      gap: 10px;
      color: #4B5563;
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;

      .export-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        border: 1px solid var(--line-3);

        .icon {
          margin-right: 4px;
        }
      }
    }
  }
}

.main-content {
  display: flex; 
  flex:1;
  min-height:0;

} 

.pdf-reader-wrapper { 
  border-right:1px solid #E5E7EB;
  position: relative;
  flex: 1;
  min-width: 300px; 
  overflow-y:auto;
  .pdf-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
} 
.review-panel {  
  position: relative;
  width: 832px;
  overflow-y:auto;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  background: var(--fill-0); 
  padding-bottom: 65px;
  .handle-result {
    position: fixed;
    bottom: 0;
    right: 6px; 
    height: 45px;
    line-height: 45px;
    padding: 0 12px;
    z-index: 10;
    width: 832px;
    background-color: var(--fill-0);
    box-shadow: 0px -2px 4px -2px #0000001A; 
    color: #374151;
    .num {
      color: #2563EB;
    }
  }
  .panel-header {
    padding: 16px; 
    font-size: 16px;
  }
  
  .filter-tabs { 
    display: flex; 
    align-items: center; 
    gap: 4px; 
    padding: 4px;
    background-color: #F5F5F5;
    border-radius: 4px;
    margin:0 16px 16px 16px;
    .filter-tab {
      display: flex;
      align-items: center; 
      justify-content: center;
      flex: 1;
      height: 38px;
      min-width: 60px; 
      border-radius: 4px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s; 
      .tab-label {
        font-size: var(--font-14);
        color: var(--text-4);
      }
      .tab-count {
        display: inline-block;
        text-align: center;
        color: #4B5563;
        border-radius: 50%;
        background-color: #F3F4F6;
        margin-left: 8px;
        padding:0 4px;
        min-width: 20px;
      }

      // 特定tab的颜色样式（适用于正常状态和骨架状态）
      &:nth-child(2) {
        .tab-count, .skeleton-count {
          background: #FEE2E2;
          color: #DC2626;
        }
      }
      &:nth-child(3) {
        .tab-count, .skeleton-count {
          background-color: #DCFCE7;
          color: #16A34A;
        }
      }

      &:hover,
      &.active {
        background: var(--fill-0);
      }

      // 骨架tab样式
      &.skeleton-tab {
        cursor: default;

        .tab-label {
          color: #9CA3AF;
        }

        .skeleton-count {
          background-color: #E5E7EB;
          color: #9CA3AF;
          animation: skeleton-loading 1.5s ease-in-out infinite;
        }

        &.active {
          background: var(--fill-0);

          .skeleton-count {
            background-color: #D1D5DB;
          }
        }

        // 骨架状态下保持特定颜色，但降低透明度
        &:nth-child(2) {
          .skeleton-count {
            background: rgba(254, 226, 226, 0.7);
            color: rgba(220, 38, 38, 0.7);
          }
        }
        &:nth-child(3) {
          .skeleton-count {
            background-color: rgba(220, 252, 231, 0.7);
            color: rgba(22, 163, 74, 0.7);
          }
        }
      }
    }
  }
  
  .review-items { 
    .item-group {
      margin-bottom: 24px; 
    }
    .skeleton-title-bar,
    .item-title-bar {
        display: flex;
        align-items: center; 
        padding: 12px 16px;
        background: #F5F5F5;
        border-bottom: 1px solid #E5E7EB; 
        .item-index {
          width: 6px;
          height: 16px;
          background-color: var(--main-6);
          border-radius: 2px;
          margin-right: 12px;
        }

        .item-title {
          font-size: var(--font-16); 
        }

        .item-count {
          color: #4B5563;
          border-radius: 50%;
          background-color: #E5E7EB;
          margin-left: 8px;
          padding:0 4px; 
          min-width: 20px; 
          text-align: center;
        }
      } 
  }
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

// 骨架屏样式
.skeleton-container {
  .skeleton-item-group {
    margin-bottom: 24px; 
    .skeleton-content {
      padding: 16px;

      .skeleton-review-item {
        padding: 16px 0;
        border-bottom: 1px solid #E5E7EB;

        &:last-child {
          border-bottom: none;
        }

        .skeleton-line {
          height: 16px;
          background: linear-gradient(90deg, #F3F4F6 25%, #E5E7EB 50%, #F3F4F6 75%);
          background-size: 200% 100%;
          border-radius: 4px;
          margin-bottom: 12px;
          animation: skeleton-loading 1.5s ease-in-out infinite;

          &:last-child {
            margin-bottom: 0;
          }

          &.skeleton-line-long {
            width: 85%;
          }

          &.skeleton-line-medium {
            width: 65%;
          }

          &.skeleton-line-short {
            width: 45%;
          }
        }
      }
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
<style lang="scss"> 
// 导出下拉菜单样式
.export-dropdown-content {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  padding: 4px 4px 12px 4px;
  min-width: 200px;

  .export-options { 
    .export-option {
      padding: 5px 12px; 
      border-radius: 4px;
      .ant-checkbox-wrapper { 
        color: #000000E0; 
      }
       &:hover {
          background-color: #0000000A;
        }
    }
  }

  .export-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 8px 8px 0 0; 
  }
}
</style>