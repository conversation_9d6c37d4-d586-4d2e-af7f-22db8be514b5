<template>
  <div class="compliance-review-container">
    <!-- 顶部导航区域 -->
    <div class="header-section">
      <div class="breadcrumb-area">
        <div class="nav-buttons">
          <a-button type="text" class="nav-btn back-btn" @click="goHome">
            <template #icon>
              <CornerUpLeft class="icon" :size="16"/>
            </template>
            首页
          </a-button>
          <a-button type="text" class="nav-btn history-btn"@click="methods.showHistoryFiles">
            <template #icon>
              <Clock8 class="icon" :size="16"/>
            </template>
          </a-button>
        </div>
        <div class="file-name">{{ fileName }}</div>
      </div>
      
      <div class="info-actions">
        <div class="review-time">
          <Calendar1  class="icon" :size="16"/>
          <span>审查时间：{{ reviewTime }}</span>
        </div>
        <div class="action-buttons">
          <a-dropdown
            v-model:open="state.exportDropdownVisible"
            :trigger="['click']"
            placement="bottomRight"
          >
            <a-button class="export-btn">
              <template #icon>
                <Download class="icon" :size="16"/>
              </template>
              导出
              <DownOutlined />
            </a-button>
            <template #overlay>
              <div class="export-dropdown-content">
                <div class="export-options">
                  <div class="export-option">
                    <a-checkbox v-model:checked="state.exportOptions.original">
                      采购文件(原始)
                    </a-checkbox>
                  </div>
                  <div class="export-option">
                    <a-checkbox v-model:checked="state.exportOptions.annotated">
                      采购文件(修订版)
                    </a-checkbox>
                  </div>
                  <div class="export-option">
                    <a-checkbox v-model:checked="state.exportOptions.report">
                      审查风险报告
                    </a-checkbox>
                  </div>
                </div>
                <div class="export-actions">
                  <a-button size="small" @click="methods.cancelExport">取消</a-button>
                  <a-button
                    type="primary"
                    size="small"
                    :disabled="!methods.hasSelectedOptions()"
                    @click="methods.confirmExport"
                  >
                    导出
                  </a-button>
                </div>
              </div>
            </template>
          </a-dropdown>
          <a-button type="primary"  @click="methods.showCheckList">
            查看审查清单
          </a-button>
        </div>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- PDF阅读器区域 -->  
      <div class="pdf-reader-wrapper">
        <PdfReader
          v-if="data.pdfUrl"
          :url="data.pdfUrl"
          :page="data.currentPage" 
          :rect="data.highlightRects" 
        />
        <div v-else class="pdf-placeholder">
          <BaseEmpty description="PDF文档加载中..." />
        </div>
      </div> 

      <!-- 审查结果面板 -->
      <div class="review-panel">
        <div class="panel-header">审查结果</div> 
        <!-- 筛选标签 - 加载时显示骨架 -->
        <div class="filter-tabs">
          <!-- 加载状态显示骨架tab -->
          <template v-if="state.loading">
            <div
              v-for="tab in SKELETON_CONFIG.tabs"
              :key="tab.key"
              :class="['filter-tab', 'skeleton-tab', { active: tab.key === 'all' }]"
            >
              <span class="tab-label">{{ tab.label }}</span>
              <span class="tab-count skeleton-count">0</span>
            </div>
          </template>
          <!-- 正常状态显示真实tab -->
          <template v-else>
            <div
              v-for="tab in filterTabs"
              :key="tab.key"
              :class="['filter-tab', tab.key, { active: state.activeFilter === tab.key }]"
              @click="methods.setActiveFilter(tab.key)"
            >
              <span class="tab-label">{{ tab.label }}</span>
              <span class="tab-count" :class="[{ active: state.activeFilter === tab.key }]">{{ tab.count  || 0}}</span>
            </div>
          </template>
        </div>

        <!-- 审查项目列表 -->
        <div class="review-items">
          <a-spin :spinning="state.loading">
            <!-- 加载状态显示骨架屏 -->
            <div v-if="state.loading || filteredItems.length === 0" class="skeleton-container">
              <div
                v-for="(category, index) in SKELETON_CONFIG.categories"
                :key="index"
                class="skeleton-item-group"
              >
                <div class="skeleton-title-bar">
                  <span class="item-index"></span>
                  <span class="item-title">{{ category.name }}</span>
                </div>
                <div class="skeleton-content">
                  <div
                    v-for="n in category.itemCount"
                    :key="n"
                    class="skeleton-review-item"
                  >
                    <div class="skeleton-line skeleton-line-long"></div>
                    <div class="skeleton-line skeleton-line-medium"></div>
                    <div class="skeleton-line skeleton-line-short"></div>
                  </div>
                </div>
              </div>
            </div> 
            <!-- 正常内容 -->
            <div v-else class="items-list">
              <!-- 项目标题栏 -->
              <div
                v-for="item in filteredItems"
                :key="item.id"
                class="item-group"
              >
                <div class="item-title-bar">
                  <span class="item-index"></span>
                  <span class="item-title">{{ item.reviewItemName }}</span>
                  <span class="item-count">{{ item.pointNum || item.children?.length || 0 }}</span>
                </div>
                <!-- 审查项目列表 -->
                <div class="sub-items">
                  <ReviewItem
                    v-for="subItem in item.children || []"
                    :key="subItem.id"
                    :item="subItem"
                    @verify="handleItemVerify"
                    @cancel="handleItemCancel"
                    @update="handleItemUpdate"
                    @edit="handleItemEdit"
                    @like="handleItemLike"
                    @dislike="handleItemDislike"
                  />
                </div>
              </div>
            </div>
          </a-spin>
        </div>
      </div>
    </div>

    <!-- 审查清单弹窗 -->
    <CheckListModal
      v-model:open="state.checkListVisible"
      :task-id="taskId"
      @save="methods.handleCheckListSave"
    />

    <!-- 历史文件弹窗 -->
    <HistoryFilesModal
      v-model="state.historyFilesVisible"
      :task-id="taskId"
      placement="left"
      @preview="methods.handleFilePreview"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { CornerUpLeft, Clock8, Calendar1, Download } from 'lucide-vue-next'
import PdfReader from '@/components/PdfReader/index.vue'
import BaseEmpty from '@/components/BaseEmpty/index.vue'
import CheckListModal from './components/CheckListModal.vue'
import HistoryFilesModal from './components/HistoryFilesModal.vue'
import ReviewItem from './components/ReviewItem.vue'
import { mockReviewTask, mockReviewItems } from './mock-data'
import type { CheckList } from '@/types/compliance'
import { apiReviewImport, commentedFile, apiDownload } from '@/api/download'
import { apiTaskResult } from '@/api/examine'

defineOptions({
  name: 'ComplianceReview'
})

const router = useRouter()
const route = useRoute()

// 获取任务ID
const taskId = ref(route.query.taskId as string || '1')

// 页面状态
const state = ref({
  loading: false,
  activeFilter: 'all',
  checkListVisible: false,
  historyFilesVisible: false,
  exportDropdownVisible: false,
  exportOptions: {
    original: true,
    annotated: true,
    report: false
  }
})

// 数据状态
const data = ref({
  reviewTask: mockReviewTask,
  reviewItems: mockReviewItems,
  pdfUrl: 'https://sppgpttest.gcycloud.cn/sftp/file/test/default/20250709/1942854607550550016/招标文件-月子中心"月嫂一对一"服务.pdf',
  currentPage: 1,
  zoomLevel: 1,
  highlightRects: []
})

// 骨架屏配置 - 使用常量避免响应式开销
const SKELETON_CONFIG = {
  categories: [
    { name: '资格公平性检查', itemCount: 3 },
    { name: '需求公平、合理性检查', itemCount: 4 },
    { name: '评审规则公平、合理性检查', itemCount: 2 }
  ],
  tabs: [
    { key: 'all', label: '全部' },
    { key: 'risk', label: '发现风险' },
    { key: 'safe', label: '未发现风险' },
    { key: 'na', label: '不适用' }
  ]
}

// 计算属性 - 简化逻辑
const filterTabs = computed(() => {
  const allItems = data.value.reviewItems.flatMap(item => item.children || [])
  const counts = {
    all: allItems.length,
    risk: allItems.filter(item => item.reviewResult === 1).length,
    safe: allItems.filter(item => item.reviewResult === 0).length,
    na: allItems.filter(item => item.reviewResult === 2).length
  }

  return [
    { key: 'all', label: '全部', count: counts.all },
    { key: 'risk', label: '发现风险', count: counts.risk },
    { key: 'safe', label: '未发现风险', count: counts.safe },
    { key: 'na', label: '不适用', count: counts.na }
  ]
})

// 过滤逻辑简化
const getFilteredChildren = (children: any[], filter: string) => {
  if (filter === 'all') return children
  const resultMap: Record<string, number> = { risk: 1, safe: 0, na: 2 }
  return children.filter(child => child.reviewResult === resultMap[filter])
}

const filteredItems = computed(() => {
  if (state.value.activeFilter === 'all') {
    return data.value.reviewItems
  }

  return data.value.reviewItems
    .map(item => ({
      ...item,
      children: getFilteredChildren(item.children || [], state.value.activeFilter)
    }))
    .filter(item => item.children.length > 0)
})

// 基本信息
const fileName = computed(() => data.value.reviewTask?.fileName || '')
const reviewTime = computed(() => data.value.reviewTask?.reviewTime || '')
 

// 导航方法
const goHome = () => {
  router.push({ name: 'HomeIndex' })
} 
// 简化的方法集合
const methods = {
  // 筛选方法
  setActiveFilter: (filterKey: string) => {
    state.value.activeFilter = filterKey
  },

  // PDF相关
  onPdfLoad: () => {
    console.log('PDF加载完成')
  },

  // 导出相关方法
  hasSelectedOptions: () => {
    const options = state.value.exportOptions
    return options.original || options.annotated || options.report
  },

  cancelExport: () => {
    state.value.exportDropdownVisible = false
  },

  confirmExport: async () => {
    try {
      const options = state.value.exportOptions
      const params = { taskId: taskId.value }
      const exportPromises = []

      if (options.original) {
        exportPromises.push(apiDownload(taskId.value))
      }
      if (options.annotated) {
        exportPromises.push(commentedFile(params))
      }
      if (options.report) {
        exportPromises.push(apiReviewImport(params))
      }

      if (exportPromises.length > 0) {
        await Promise.all(exportPromises)
        message.success('导出成功')
        state.value.exportDropdownVisible = false
      }
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  },

  // 弹窗控制
  showCheckList: () => {
    state.value.checkListVisible = true
  },

  showHistoryFiles: () => {
    state.value.historyFilesVisible = true
  },

  // 文件预览
  handleFilePreview: (file: any) => {
    message.info(`预览文件: ${file.name}`)
  },

  // 审查清单保存
  handleCheckListSave: (checkList: CheckList) => {
    console.log('保存审查清单:', checkList)
    message.success('审查清单已保存')
  }
}

// ReviewItem事件处理方法
const handleItemVerify = (item: any) => {
  console.log('核实项目:', item)
  message.info('核实功能开发中...')
}

const handleItemCancel = (item: any) => {
  console.log('取消项目:', item)
  message.info('取消功能开发中...')
}

const handleItemLike = (item: any, reason?: string) => {
  console.log('点赞项目:', item, '原因:', reason)

  if (reason === '') {
    // 取消点赞
    if (item.likeCount !== undefined && item.likeCount > 0) {
      item.likeCount--
    }
    message.success('已取消点赞')
  } else {
    // 点赞
    if (item.likeCount !== undefined) {
      item.likeCount++
    } else {
      item.likeCount = 1
    }
    message.success('点赞成功')
  }
}

const handleItemDislike = (item: any, reason?: string) => {
  console.log('点踩项目:', item, '反馈原因:', reason)

  if (reason === '') {
    // 取消点踩
    if (item.dislikeCount !== undefined && item.dislikeCount > 0) {
      item.dislikeCount--
    }
    message.success('已取消点踩')
  } else {
    // 点踩
    if (item.dislikeCount !== undefined) {
      item.dislikeCount++
    } else {
      item.dislikeCount = 1
    }

    if (reason && reason.trim()) {
      // 显示反馈原因的简短版本
      const shortReason = reason.length > 20 ? reason.substring(0, 20) + '...' : reason
      message.success(`点踩成功，感谢您的反馈：${shortReason}`)

      // 这里可以调用API保存详细的反馈信息
      // await apiSaveDislikeFeedback(item.id, reason)
    } else {
      message.success('点踩成功')
    }
  }
}

// 处理项目更新
const handleItemUpdate = (updatedItem: any) => {
  console.log('更新项目:', updatedItem)
  data.value.reviewItems.forEach(group => {
    if (group.children) {
      const index = group.children.findIndex((item: any) => item.id === updatedItem.id)
      if (index !== -1) {
        group.children[index] = { ...group.children[index], ...updatedItem }
      }
    }
  })
}

// 处理编辑建议
const handleItemEdit = (item: any) => {
  console.log('编辑建议:', item)
}
</script>

<style lang="scss" scoped>
.compliance-review-container {
  color: #111827;
  height: 100vh;
  overflow:hidden;
  display:flex;
  flex-direction:column;
}

.header-section {
  flex-shrink:0;
  display: flex;
  align-items: center;
  justify-content: space-between; 
  padding: 11px 24px;
  border-bottom: 1px solid var(--line-2); 
  box-sizing:border-box;
  .breadcrumb-area {
    display: flex;
    align-items: center;  
    .nav-buttons {
      display: flex;
      gap: 16px;
      .nav-btn {
        display: flex;
        align-items: center; 
        &.back-btn { 
          padding: 8px 16px;
          border: 1px solid var(--line-3);
          border-radius: 4px;
          .icon {
            margin-right: 8px;
          }
        }
        &.history-btn{
          &:hover {
            background-color: transparent;
          }
        }
      }
    } 
  }
  
  .info-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    .review-time {
      display: flex;
      align-items: center;
      gap: 10px;
      color: #4B5563;
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;

      .export-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        border: 1px solid var(--line-3);

        .icon {
          margin-right: 4px;
        }
      }
    }
  }
}

.main-content {
  display: flex; 
  flex:1;
  min-height:0;

}
.pdf-reader-wrapper,
.review-panel {
  flex: 1;
  min-width: 300px; 
  overflow-y:auto;
}
.pdf-reader-wrapper { 
  border-right:1px solid #E5E7EB;
  position: relative;

  .pdf-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
} 
.review-panel { 
  max-width: 832px;
  display: flex;
  flex-direction: column;
  background: var(--fill-0);
  
  .panel-header {
    padding: 16px; 
    font-size: 16px;
  }
  
  .filter-tabs { 
    display: flex; 
    align-items: center; 
    gap: 4px; 
    padding: 4px;
    background-color: #F5F5F5;
    border-radius: 4px;
    margin:0 16px 16px 16px;
    .filter-tab {
      display: flex;
      align-items: center; 
      justify-content: center;
      flex: 1;
      height: 38px;
      min-width: 60px; 
      border-radius: 4px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s; 
      .tab-label {
        font-size: var(--font-14);
        color: var(--text-4);
      }
      .tab-count {
        display: inline-block;
        text-align: center;
        color: #4B5563;
        border-radius: 50%;
        background-color: #F3F4F6;
        margin-left: 8px;
        padding:0 4px;
        min-width: 20px;
      }

      // 特定tab的颜色样式（适用于正常状态和骨架状态）
      &:nth-child(2) {
        .tab-count, .skeleton-count {
          background: #FEE2E2;
          color: #DC2626;
        }
      }
      &:nth-child(3) {
        .tab-count, .skeleton-count {
          background-color: #DCFCE7;
          color: #16A34A;
        }
      }

      &:hover,
      &.active {
        background: var(--fill-0);
      }

      // 骨架tab样式
      &.skeleton-tab {
        cursor: default;

        .tab-label {
          color: #9CA3AF;
        }

        .skeleton-count {
          background-color: #E5E7EB;
          color: #9CA3AF;
          animation: skeleton-loading 1.5s ease-in-out infinite;
        }

        &.active {
          background: var(--fill-0);

          .skeleton-count {
            background-color: #D1D5DB;
          }
        }

        // 骨架状态下保持特定颜色，但降低透明度
        &:nth-child(2) {
          .skeleton-count {
            background: rgba(254, 226, 226, 0.7);
            color: rgba(220, 38, 38, 0.7);
          }
        }
        &:nth-child(3) {
          .skeleton-count {
            background-color: rgba(220, 252, 231, 0.7);
            color: rgba(22, 163, 74, 0.7);
          }
        }
      }
    }
  }
  
  .review-items { 
    .item-group {
      margin-bottom: 24px; 
    }
    .skeleton-title-bar,
    .item-title-bar {
        display: flex;
        align-items: center; 
        padding: 12px 16px;
        background: #F5F5F5;
        border-bottom: 1px solid #E5E7EB; 
        .item-index {
          width: 6px;
          height: 16px;
          background-color: var(--main-6);
          border-radius: 2px;
          margin-right: 12px;
        }

        .item-title {
          font-size: var(--font-16); 
        }

        .item-count {
          color: #4B5563;
          border-radius: 50%;
          background-color: #E5E7EB;
          margin-left: 8px;
          padding:0 4px; 
          min-width: 20px; 
        }
      } 
  }
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

// 骨架屏样式
.skeleton-container {
  .skeleton-item-group {
    margin-bottom: 24px; 
    .skeleton-content {
      padding: 16px;

      .skeleton-review-item {
        padding: 16px 0;
        border-bottom: 1px solid #E5E7EB;

        &:last-child {
          border-bottom: none;
        }

        .skeleton-line {
          height: 16px;
          background: linear-gradient(90deg, #F3F4F6 25%, #E5E7EB 50%, #F3F4F6 75%);
          background-size: 200% 100%;
          border-radius: 4px;
          margin-bottom: 12px;
          animation: skeleton-loading 1.5s ease-in-out infinite;

          &:last-child {
            margin-bottom: 0;
          }

          &.skeleton-line-long {
            width: 85%;
          }

          &.skeleton-line-medium {
            width: 65%;
          }

          &.skeleton-line-short {
            width: 45%;
          }
        }
      }
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
<style lang="scss"> 
// 导出下拉菜单样式
.export-dropdown-content {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  padding: 4px 4px 12px 4px;
  min-width: 200px;

  .export-options { 
    .export-option {
      padding: 5px 12px; 
      border-radius: 4px;
      .ant-checkbox-wrapper { 
        color: #000000E0; 
      }
       &:hover {
          background-color: #0000000A;
        }
    }
  }

  .export-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 8px 8px 0 0; 
  }
}
</style>